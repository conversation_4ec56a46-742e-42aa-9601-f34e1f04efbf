# CareAXL Scheduling Engine: Technical Reference Guide

## Table of Contents
1. [Timefold Constraint Solver Fundamentals](#1-timefold-constraint-solver-fundamentals)
2. [Two-Stage Optimization Architecture](#2-two-stage-optimization-architecture)
3. [Domain Models and Planning Entities](#3-domain-models-and-planning-entities)
4. [Constraint System Architecture](#4-constraint-system-architecture)
5. [Configuration Management System](#5-configuration-management-system)
6. [Scheduler Parameterization and Execution](#6-scheduler-parameterization-and-execution)
7. [Service Integration and Event Architecture](#7-service-integration-and-event-architecture)
8. [Daily Solver Operations](#8-daily-solver-operations)
9. [Adding New Constraints](#9-adding-new-constraints)
10. [External System Integration](#10-external-system-integration)
11. [Warm Solver Performance Optimization](#11-warm-solver-performance-optimization)

---

## 1. Timefold Constraint Solver Fundamentals

### 1.1 Timefold Overview

Timefold is an AI-powered constraint solver that optimizes complex scheduling problems by evaluating millions of combinations against business rules.

```
┌─────────────────────────────────────────────────────────────┐
│                    TIMEFOLD SOLVER PROCESS                  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  INPUT DATA          SOLVER ENGINE           OUTPUT         │
│  ┌─────────────┐    ┌─────────────────┐    ┌─────────────┐  │
│  │ Planning    │───▶│ Constraint      │───▶│ Optimized   │  │
│  │ Entities    │    │ Evaluation      │    │ Solution    │  │
│  │             │    │                 │    │             │  │
│  │ Problem     │    │ Search          │    │ Score:      │  │
│  │ Facts       │    │ Algorithms      │    │ 0hard/      │  │
│  │             │    │                 │    │ -150soft    │  │
│  │ Value       │    │ Score           │    │             │  │
│  │ Ranges      │    │ Calculation     │    │             │  │
│  └─────────────┘    └─────────────────┘    └─────────────┘  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 Core Concepts

```
┌─────────────────────────────────────────────────────────────┐
│                 TIMEFOLD CORE CONCEPTS                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  PLANNING ENTITIES (What to optimize)                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  @planning_entity                                       │ │
│  │  class AppointmentAssignment:                           │ │
│  │      id: str                                            │ │
│  │      appointment_data: AppointmentData                  │ │
│  │      provider: Provider = None      ← PLANNING VARIABLE │ │
│  │      assigned_date: date = None     ← PLANNING VARIABLE │ │
│  │                                                         │ │
│  │  • Each appointment needs provider + date assignment    │ │
│  │  • Solver tries different combinations                  │ │
│  │  • Variables start unassigned (None)                    │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  PROBLEM FACTS (Fixed data)                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  @planning_solution                                     │ │
│  │  class AppointmentSchedule:                             │ │
│  │      providers: List[Provider]      ← VALUE RANGE      │ │
│  │      available_dates: List[date]    ← VALUE RANGE      │ │
│  │      appointments: List[Assignment] ← ENTITIES         │ │
│  │      score: HardSoftScore = None    ← SOLUTION SCORE   │ │
│  │                                                         │ │
│  │  • Providers/dates are fixed options                   │ │
│  │  • Solver selects from these ranges                    │ │
│  │  • Score measures solution quality                     │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  CONSTRAINTS (Business rules)                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  @with_config('skilled_nursing')                        │ │
│  │  def provider_skill_match(factory):                     │ │
│  │      return (factory.for_each(AppointmentAssignment)    │ │
│  │              .filter(lambda a: not has_skills(a))       │ │
│  │              .penalize(HardSoftScore.ONE_HARD)          │ │
│  │              .as_constraint("Skill match required"))    │ │
│  │                                                         │ │
│  │  • HARD constraint: Must be satisfied                  │ │
│  │  • Penalizes invalid skill assignments                 │ │
│  │  • @with_config injects configuration                  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 1.3 Constraint Types and Scoring

```
┌─────────────────────────────────────────────────────────────┐
│                    CONSTRAINT SCORING SYSTEM               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  HARD CONSTRAINTS (Must be satisfied)                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ • Provider skill validation                             │ │
│  │ • Date availability                                     │ │
│  │ • Geographic service area                               │ │
│  │ • No double booking                                     │ │
│  │                                                         │ │
│  │ Score Impact: -1hard per violation                      │ │
│  │ Result: Solution is infeasible if any hard violated     │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  SOFT CONSTRAINTS (Optimization goals)                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ • Workload balancing                                    │ │
│  │ • Geographic clustering                                 │ │
│  │ • Continuity of care                                    │ │
│  │ • Travel time minimization                              │ │
│  │                                                         │ │
│  │ Score Impact: -1soft to -100soft per violation         │ │
│  │ Result: Better scores = better optimization             │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  SCORE COMPARISON                                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Solution A: 0hard/-150soft  ← Better (feasible)        │ │
│  │ Solution B: -1hard/-50soft  ← Worse (infeasible)       │ │
│  │ Solution C: 0hard/-200soft  ← Worse (less optimized)   │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 2. Two-Stage Optimization Architecture

### 2.1 Architecture Overview

The system uses a two-stage approach to manage complexity and optimize different aspects separately.

```
┌─────────────────────────────────────────────────────────────┐
│                TWO-STAGE OPTIMIZATION FLOW                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  INPUT: Appointment Requests                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ • Patient needs skilled nursing visit                   │ │
│  │ • Required skills: ["wound_care", "medication"]         │ │
│  │ • Preferred dates: July 1-7, 2024                       │ │
│  │ • Location: 123 Main St                                 │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  STAGE 1: ASSIGNMENT SOLVER                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Goal: Assign provider and date                          │ │
│  │                                                         │ │
│  │ Input:  AppointmentAssignment entities                  │ │
│  │ Output: Provider + Date assignments                     │ │
│  │                                                         │ │
│  │ Date Assignment Horizon:                                │ │
│  │ • Rolling window of N days (configurable)              │ │
│  │ • Default: 7 days from current date                     │ │
│  │ • Configurable via `rolling_window_days` in scheduler.yml│ │
│  │                                                         │ │
│  │ Constraints:                                            │ │
│  │ • C001: Provider skill validation                       │ │
│  │ • C002: Date availability                               │ │
│  │ • C003: Geographic service area                         │ │
│  │ • C004: Timed visit date assignment                     │ │
│  │ • C005: Workload balancing                              │ │
│  │ • C006: Geographic clustering                           │ │
│  │ • C007: Patient preference matching                     │ │
│  │ • C008: Provider capacity management                    │ │
│  │ • C009: Continuity of care optimization                 │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  STAGE 2: DAY PLAN SOLVER                                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Goal: Assign time slots and optimize routes             │ │
│  │                                                         │ │
│  │ Input:  TimeSlotAssignment entities                     │ │
│  │         (Provider appointments for current date only)   │ │
│  │ Output: Time slots + Visit orders                       │ │
│  │         (Daily schedule with optimized visit sequence)  │ │
│  │                                                         │ │
│  │ Constraints:                                            │ │
│  │ • C010: Time slot availability validation               │ │
│  │ • C011: No appointment overlap                          │ │
│  │ • C012: Flexible appointment timing optimization       │ │
│  │ • C013: Healthcare task sequencing                     │ │
│  │ • C014: Travel time optimization                        │ │
│  │ • C015: Timed appointment pinning                       │ │
│  │ • C016: Route optimization                              │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  OUTPUT: Complete Schedule                                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ • Provider: Sarah Johnson (RN)                          │ │
│  │ • Date: July 1, 2024                                    │ │
│  │ • Time: 9:00 AM - 10:00 AM                              │ │
│  │ • Visit order: 2nd appointment of the day               │ │
│  │ • Route: Optimized for minimal travel time              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 Stage 1: Assignment Solver

```
┌─────────────────────────────────────────────────────────────┐
│                    ASSIGNMENT SOLVER PROCESS               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  EXECUTION: assign_appointments.py                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  class AssignAppointmentJob:                            │ │
│  │      def run(self, service_type, date_range):           │ │
│  │          # Load appointment requests                    │ │
│  │          appointments = load_appointments()             │ │
│  │                                                         │ │
│  │          # Create planning entities                     │ │
│  │          assignments = [AppointmentAssignment(apt)      │ │
│  │                        for apt in appointments]        │ │
│  │                                                         │ │
│  │          # Configure solver                             │ │
│  │          solver = SolverFactory.create(                 │ │
│  │              solution_class=AppointmentSchedule,        │ │
│  │              constraint_provider=define_constraints     │ │
│  │          )                                              │ │
│  │                                                         │ │
│  │          # Solve and return results                     │ │
│  │          solution = solver.solve(problem)               │ │
│  │          return process_assignment_results(solution)    │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.3 Stage 2: Day Plan Solver

```
┌─────────────────────────────────────────────────────────────┐
│                    DAY PLAN SOLVER PROCESS                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  EXECUTION: day_plan.py                                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  class DayPlanJob:                                      │ │
│  │      def run(self, target_date):                        │ │
│  │          # Load assignments from Stage 1                │ │
│  │          assignments = load_assignments(target_date)    │ │
│  │                                                         │ │
│  │          # Transform to time slot entities              │ │
│  │          time_assignments = [                           │ │
│  │              TimeSlotAssignment(                        │ │
│  │                  scheduled_appointment=create_scheduled(│ │
│  │                      assignment                         │ │
│  │                  )                                      │ │
│  │              ) for assignment in assignments            │ │
│  │          ]                                              │ │
│  │                                                         │ │
│  │          # Configure day solver                         │ │
│  │          solver = SolverFactory.create(                 │ │
│  │              solution_class=DaySchedule,                │ │
│  │              constraint_provider=define_day_constraints │ │
│  │          )                                              │ │
│  │                                                         │ │
│  │          # Solve and return results                     │ │
│  │          solution = solver.solve(day_schedule)          │ │
│  │          return process_day_plan_results(solution)      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 3. Domain Models and Planning Entities

### 3.1 Model Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    MODEL ARCHITECTURE                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  DOMAIN MODELS (domain.py)                                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Framework-agnostic business entities                    │ │
│  │                                                         │ │
│  │ • Provider                                              │ │
│  │ • Consumer                                              │ │
│  │ • AppointmentData                                       │ │
│  │ • Location                                              │ │
│  │ • ServiceConfig                                         │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                              │
│                              ▼                              │
│  PLANNING MODELS (planning_models.py)                      │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Timefold-specific entities with annotations             │ │
│  │                                                         │ │
│  │ • AppointmentAssignment (@planning_entity)              │ │
│  │ • TimeSlotAssignment (@planning_entity)                 │ │
│  │ • AppointmentSchedule (@planning_solution)              │ │
│  │ • DaySchedule (@planning_solution)                      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Core Domain Models

````python
@dataclass
class Provider:
    """Healthcare provider (nurse, therapist, etc.)"""
    id: str
    name: str
    role: str  # "RN", "LPN", "CNA", "PT", "OT"
    skills: List[str]
    home_location: Optional[Location] = None
    availability: Optional[ProviderAvailability] = None
    capacity: Optional[ProviderCapacity] = None
    preferences: Optional[ProviderPreferences] = None

@dataclass
class AppointmentData:
    """Core appointment information"""
    id: str
    patient_id: str
    service_type: str
    required_skills: List[str]
    required_role: Optional[str] = None
    duration_min: int = 60
    location: Optional[Location] = None
    timing: Optional[AppointmentTiming] = None
    status: AppointmentStatus = AppointmentStatus.REQUESTED
````

### 3.3 Planning Entities

````python
@planning_entity
@dataclass
class AppointmentAssignment:
    """Planning entity for Stage 1: Assignment Solver"""
    id: Annotated[str, PlanningId]
    appointment_data: AppointmentData
    provider: Annotated[Optional[Provider], PlanningVariable] = field(default=None)
    assigned_date: Annotated[Optional[date], PlanningVariable] = field(default=None)

@planning_entity
@dataclass
class TimeSlotAssignment:
    """Planning entity for Stage 2: Day Plan Solver"""
    id: Annotated[str, PlanningId]
    scheduled_appointment: ScheduledAppointment
    time_slot: Annotated[Optional[time], PlanningVariable] = field(default=None)
    visit_order: Annotated[Optional[int], PlanningVariable] = field(default=None)
````

### 3.4 Planning Solutions

````python
@planning_solution
@dataclass
class AppointmentSchedule:
    """Solution for Stage 1: Assignment optimization"""
    id: str
    providers: Annotated[List[Provider], ProblemFactCollectionProperty, ValueRangeProvider]
    available_dates: Annotated[List[date], ProblemFactCollectionProperty, ValueRangeProvider]
    appointment_assignments: Annotated[List[AppointmentAssignment], PlanningEntityCollectionProperty]
    score: Annotated[Optional[HardSoftScore], PlanningScore] = field(default=None)

@planning_solution
@dataclass
class DaySchedule:
    """Solution for Stage 2: Day planning optimization"""
    id: str
    date: date
    time_slots: Annotated[List[time], ProblemFactCollectionProperty, ValueRangeProvider]
    scheduled_appointments: Annotated[List[ScheduledAppointment], ProblemFactCollectionProperty]
    time_assignments: Annotated[List[TimeSlotAssignment], PlanningEntityCollectionProperty]
    score: Annotated[Optional[HardSoftScore], PlanningScore] = field(default=None)
````

---

## 4. Constraint System Architecture

### 4.1 Constraint Organization

```
┌─────────────────────────────────────────────────────────────┐
│                 CONSTRAINT FILE STRUCTURE                  │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  src/constraints/                                           │
│  ├── assignment_constraints.py    ← Stage 1 coordinator     │
│  ├── day_constraints.py          ← Stage 2 coordinator     │
│  ├── base_constraints.py         ← @with_config decorator  │
│  ├── config_registry.py          ← Configuration system    │
│  │                                                         │
│  ├── c001_asgn_provider_skill_validation.py                │
│  ├── c002_asgn_date_based_availability.py                  │
│  ├── c003_asgn_geographic_service_area.py                  │
│  ├── c004_asgn_timed_visit_date_assignment.py              │
│  ├── c005_asgn_workload_balance_optimization.py            │
│  ├── c006_asgn_geographic_clustering_optimization.py       │
│  ├── c007_asgn_patient_preference_matching.py              │
│  ├── c008_asgn_provider_capacity_management.py             │
│  ├── c009_asgn_continuity_of_care_optimization.py          │
│  │                                                         │
│  ├── c010_schd_timeslot_availability_validation.py         │
│  ├── c011_schd_appointment_overlap_prevention.py           │
│  ├── c012_schd_flexible_appointment_timing_optimization.py │
│  ├── c013_schd_healthcare_task_sequencing.py               │
│  ├── c014_schd_route_travel_time_optimization.py           │
│  ├── c015_schd_timed_appointment_pinning.py                │
│  └── c016_schd_route_optimization.py                       │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 Constraint Registration Flow

```
┌─────────────────────────────────────────────────────────────┐
│                CONSTRAINT REGISTRATION FLOW                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  STAGE 1: Assignment Constraints                            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  assignment_constraints.py                              │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ @constraint_provider                                │ │ │
│  │  │ def define_constraints(factory):                    │ │ │
│  │  │     constraints = []                                │ │ │
│  │  │                                                     │ │ │
│  │  │     # Hard constraints                              │ │ │
│  │  │     constraints.extend(                             │ │ │
│  │  │         provider_skill_validation_constraints(      │ │ │
│  │  │             factory))                               │ │ │
│  │  │     constraints.extend(                             │ │ │
│  │  │         date_based_availability_constraints(        │ │ │
│  │  │             factory))                               │ │ │
│  │  │                                                     │ │ │
│  │  │     # Soft constraints                              │ │ │
│  │  │     constraints.extend(                             │ │ │
│  │  │         workload_balance_optimization_constraints(  │ │ │
│  │  │             factory))                               │ │ │
│  │  │                                                     │ │ │
│  │  │     return constraints                              │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  STAGE 2: Day Planning Constraints                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  day_constraints.py                                     │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ @constraint_provider                                │ │ │
│  │  │ def define_day_constraints(factory):                │ │ │
│  │  │     constraints = []                                │ │ │
│  │  │                                                     │ │ │
│  │  │     # Hard constraints                              │ │ │
│  │  │     constraints.extend(                             │ │ │
│  │  │         appointment_overlap_prevention_constraints( │ │ │
│  │  │             factory))                               │ │ │
│  │  │                                                     │ │ │
│  │  │     # Soft constraints                              │ │ │
│  │  │     constraints.extend(                             │ │ │
│  │  │         route_optimization_constraints(factory))    │ │ │
│  │  │                                                     │ │ │
│  │  │     return constraints                              │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 4.3 Configuration Injection System

````python
def with_config(service_name, return_type='constraint'):
    """
    Decorator for constraints that need configuration.
    Automatically provides core_config, service_config, and combined_config.
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Runtime service override
            runtime_service = get_current_service_type()
            actual_service = runtime_service if runtime_service else service_name

            # Get configurations
            ConfigRegistry.set_current_service_context(actual_service)
            core_config = ConfigRegistry.get_core_config('scheduler')
            service_config = ConfigRegistry.get_service_config(actual_service)

            # Create combined config (service overrides core)
            combined_config = {**(core_config or {}), **(service_config or {})}

            # Call function with all configs
            return func(*args, core_config=core_config,
                       service_config=service_config,
                       combined_config=combined_config, **kwargs)
        return wrapper
    return decorator
````

### 4.4 Constraint Implementation Pattern

**Simplified Decorator Usage:**
The `@with_config()` decorator now supports a simplified syntax where the program type can be omitted and will use the default from `scheduler.yml`.

````python
# Simplified usage (recommended)
@with_config()  # Uses default_program_type from scheduler.yml
def provider_skill_validation(factory: ConstraintFactory,
                             core_config=None,
                             service_config=None,
                             combined_config=None,
                             **kwargs) -> Constraint:
    """Providers must have all required skills for their assigned appointments."""
    # Handle Timefold validation calls
    if factory is None:
        return factory.for_each(AppointmentAssignment).filter(
            lambda x: False).as_constraint("no_op_constraint")

    # Use configuration
    skill_validation_enabled = core_config.get('enable_skill_validation', True)
    if not skill_validation_enabled:
        return factory.for_each(AppointmentAssignment).filter(
            lambda x: False).as_constraint("skill_validation_disabled")

    return (
        factory.for_each(AppointmentAssignment)
        .filter(lambda assignment: (
            assignment.provider is not None and
            assignment.appointment_data.required_skills is not None and
            not _has_required_skills(assignment.provider,
                                   assignment.appointment_data.required_skills)
        ))
        .penalize(HardSoftScore.ONE_HARD, lambda assignment: 1)
        .as_constraint("Provider skill validation")
    )
````

---

## 5. Configuration Management System

### 5.1 Hierarchical Configuration Architecture

```
┌─────────────────────────────────────────────────────────────┐
│              HIERARCHICAL CONFIGURATION SYSTEM             │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  CONFIGURATION HIERARCHY                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  CORE CONFIG (scheduler.yml)                            │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ # Global settings                                   │ │ │
│  │  │ rolling_window_days: 7                              │ │ │
│  │  │ # ↑ Controls assignment horizon (days from today)   │ │ │
│  │  │ max_solving_time_seconds: 300                       │ │ │
│  │  │ default_program_type: "skilled_nursing"             │ │ │
│  │  │ # ↑ Default for @with_config() decorator           │ │ │
│  │  │ enable_geographic_clustering: true                  │ │ │
│  │  │ enable_continuity_of_care: true                     │ │ │
│  │  │ enable_workload_balancing: true                     │ │ │
│  │  │ # Warm solver performance optimization              │ │ │
│  │  │ warm_solver:                                        │ │ │
│  │  │   enabled: false                                    │ │ │
│  │  │   cache_directory: "cache/warm_solver"              │ │ │
│  │  │   max_cache_age_hours: 24                           │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼ OVERRIDES                │ │
│  │  PROGRAM CONFIG (skilled_nursing.yml)                   │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ # Program-specific settings                         │ │ │
│  │  │ program_type: skilled_nursing                       │ │ │
│  │  │ required_skills:                                    │ │ │
│  │  │   - "medication_administration"                     │ │ │
│  │  │   - "wound_care"                                    │ │ │
│  │  │ geographic_radius_miles: 25.0                       │ │ │
│  │  │ max_daily_appointments_per_provider: 8              │ │ │
│  │  │ continuity_weight: 0.8                              │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼ RUNTIME MERGE            │ │
│  │  COMBINED CONFIG                                        │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ # Merged at runtime (program overrides core)        │ │ │
│  │  │ rolling_window_days: 7              ← from core     │ │ │
│  │  │ # ↑ Assignment horizon: today + 7 days              │ │ │
│  │  │ program_type: skilled_nursing       ← from program  │ │ │
│  │  │ enable_continuity_of_care: true     ← from core     │ │ │
│  │  │ continuity_weight: 0.8              ← from program  │ │ │
│  │  │ warm_solver.enabled: false          ← from core     │ │ │
│  │  │ warm_solver.cache_directory: "cache/warm_solver"    │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 5.2 Configuration Integration with Provider Data

```
┌─────────────────────────────────────────────────────────────┐
│           CONFIGURATION + PROVIDER DATA INTEGRATION        │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  RUNTIME CONFIGURATION FLOW                                │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  1. LOAD CONFIGURATIONS                                 │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ ConfigRegistry.load_configurations()            │ │ │
│  │     │ ├─ scheduler.yml → core_config                  │ │ │
│  │     │ ├─ skilled_nursing.yml → program_config         │ │ │
│  │     │ ├─ behavioral_care.yml → program_config         │ │ │
│  │     │ ├─ hospital_at_home.yml → program_config        │ │ │
│  │     │ └─ pcs.yml → program_config                      │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  2. PROVIDER DATA INTEGRATION                           │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ Provider preferences override config defaults   │ │ │
│  │     │                                                 │ │ │
│  │     │ Provider: Sarah Johnson                         │ │ │
│  │     │ ├─ Service: skilled_nursing                     │ │ │
│  │     │ ├─ Preferred radius: 20 miles                   │ │ │
│  │     │ │  (overrides config: 25 miles)                 │ │ │
│  │     │ ├─ Max daily appointments: 6                    │ │ │
│  │     │ │  (overrides config: 8)                        │ │ │
│  │     │ └─ Continuity bonus: 120                        │ │ │
│  │     │    (overrides config: 100)                      │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  3. CONSTRAINT EVALUATION                               │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ @with_config()  # Uses default_program_type     │ │ │
│  │     │ def geographic_clustering(factory,              │ │ │
│  │     │                          program_config,        │ │ │
│  │     │                          **kwargs):             │ │ │
│  │     │                                                 │ │ │
│  │     │     # Get base radius from config               │ │ │
│  │     │     base_radius = program_config.get(           │ │ │
│  │     │         'geographic_radius_miles', 25.0)        │ │ │
│  │     │                                                 │ │ │
│  │     │     def calculate_penalty(assignment):          │ │ │
│  │     │         provider = assignment.provider          │ │ │
│  │     │         # Use provider preference if available  │ │ │
│  │     │         radius = provider.preferences.          │ │ │
│  │     │                 preferred_radius or base_radius │ │ │
│  │     │         return distance_penalty(assignment,     │ │ │
│  │     │                               radius)           │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 5.3 Feature Toggle System (Core Configuration)

````yaml
# Core Configuration (scheduler.yml)
# Feature Toggles - Control constraint activation globally
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: true
enable_provider_capacity_management: true

# Day Planning Stage Feature Toggles
enable_healthcare_task_sequencing: true
enable_travel_time_optimization: true
enable_break_time_management: true
enable_route_optimization: true

# Advanced Features
enable_advanced_traffic_integration: true
````

````python
@with_config()  # Uses default_program_type from scheduler.yml
def workload_balance_optimization(factory, core_config=None, **kwargs):
    """Balance workload across providers."""

    # Check feature toggle from core configuration (scheduler.yml)
    if not core_config.get('enable_workload_balancing', True):
        return factory.for_each(AppointmentAssignment).filter(
            lambda x: False).as_constraint("workload_balancing_disabled")

    # Implementation continues...
````

### 5.4 Warm Solver Configuration

The warm solver feature provides performance optimization through state persistence and incremental updates. It enables faster startup times and better convergence by reusing previous solver states.

````yaml
# Warm Solver Configuration (scheduler.yml)
warm_solver:
  enabled: false  # Default to false for backward compatibility
  cache_directory: "cache/warm_solver"
  max_cache_age_hours: 24
  incremental_updates: true
  preserve_solver_state: true
````

**Configuration Options:**

| Option | Default | Description |
|--------|---------|-------------|
| `enabled` | `false` | Enable warm solver functionality |
| `cache_directory` | `"cache/warm_solver"` | Directory for storing cached states |
| `max_cache_age_hours` | `24` | Maximum age of cached states before expiration |
| `incremental_updates` | `true` | Enable incremental update functionality |
| `preserve_solver_state` | `true` | Preserve solver state between runs |

**Benefits:**
- **Faster Startup**: Reuses cached solver state instead of cold start
- **Better Convergence**: Builds on previous optimization results
- **Incremental Updates**: Handles new appointments efficiently
- **Backward Compatibility**: Disabled by default, opt-in feature

---

## 6. Scheduler Parameterization and Execution

### 6.1 Scheduler Architecture

````python
class SchedulerMain:
    """Main scheduler class with command-line interface."""

    def __init__(self):
        self.config_registry = ConfigRegistry()
        self.assign_job = AssignAppointmentJob()
        self.day_plan_job = DayPlanJob()

    def run_assignment_job(self, service_type: str, date_range: DateRange):
        """Run Stage 1: Assignment optimization."""
        return self.assign_job.run(service_type, date_range)

    def run_day_plan_job(self, target_date: date):
        """Run Stage 2: Day planning optimization."""
        return self.day_plan_job.run(target_date)
````

### 6.2 Command-Line Parameters

```
┌─────────────────────────────────────────────────────────────┐
│                 SCHEDULER COMMAND-LINE INTERFACE           │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  AVAILABLE OPTIONS                                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  --mode: Execution mode                                 │ │
│  │    • daemon: Continuous scheduled execution             │ │
│  │    • once: Single job execution                         │ │
│  │                                                         │ │
│  │  --job: Job type (required for once mode)              │ │
│  │    • assign: Run AssignAppointment job                  │ │
│  │    • dayplan: Run DayPlan job                           │ │
│  │                                                         │ │
│  │  --date: Target date for dayplan job                    │ │
│  │    Format: YYYY-MM-DD (defaults to today)              │ │
│  │                                                         │ │
│  │  --config-folder: Configuration directory               │ │
│  │    Default: "config"                                    │ │
│  │                                                         │ │
│  │  --warm-solver: Enable warm solver mode                 │ │
│  │    • Faster startup and better convergence              │ │
│  │    • Requires warm_solver.enabled=true in config       │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  EXECUTION EXAMPLES                                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  # Run assignment job once                              │ │
│  │  python scheduler.py --mode once --job assign           │ │
│  │                                                         │ │
│  │  # Run day planning job once                            │ │
│  │  python scheduler.py --mode once --job dayplan          │ │
│  │                     --date 2024-07-01                   │ │
│  │                                                         │ │
│  │  # Run with warm solver enabled                         │ │
│  │  python scheduler.py --mode once --job assign           │ │
│  │                     --warm-solver                       │ │
│  │                                                         │ │
│  │  # Run as daemon (scheduled execution)                  │ │
│  │  python scheduler.py --mode daemon                      │ │
│  │                                                         │ │
│  │  # Run with custom config folder                        │ │
│  │  python scheduler.py --mode once --job assign           │ │
│  │                     --config-folder /path/to/config     │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 6.3 Execution Modes

```
┌─────────────────────────────────────────────────────────────┐
│                    EXECUTION MODE FLOWS                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ONCE MODE (Single execution)                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Start → Load Config → Run Job → Output Results → Exit  │ │
│  │                                                         │ │
│  │  Use cases:                                             │ │
│  │  • Manual optimization runs                             │ │
│  │  • Testing and development                              │ │
│  │  • Ad-hoc schedule adjustments                          │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  DAEMON MODE (Scheduled execution)                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Start → Load Config → Schedule Jobs → Wait → Execute   │ │
│  │    ▲                                           │        │ │
│  │    └───────────────────────────────────────────┘        │ │
│  │                                                         │ │
│  │  Schedule:                                              │ │
│  │  • 02:00 AM: Assignment job (nightly)                  │ │
│  │  • 06:00 AM: Day plan job (daily)                      │ │
│  │                                                         │ │
│  │  Use cases:                                             │ │
│  │  • Production deployment                                │ │
│  │  • Automated scheduling                                 │ │
│  │  • Continuous optimization                              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```



---

## 7. Service Integration and Event Architecture

### 7.1 Hybrid Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                 HYBRID SERVICE ARCHITECTURE                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  PARALLEL INPUT PROCESSING                                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  SYNCHRONOUS (REST API)        ASYNCHRONOUS (Events)    │ │
│  │  ┌─────────────────────────┐   ┌─────────────────────────┐ │ │
│  │  │ External System         │   │ RabbitMQ Event Bus      │ │ │
│  │  │ ┌─────────────────────┐ │   │ ┌─────────────────────┐ │ │ │
│  │  │ │ POST /api/schedule  │ │   │ │ CRITICAL Queue      │ │ │ │
│  │  │ │ {                   │ │   │ │ HIGH Queue          │ │ │ │
│  │  │ │   "operation":      │ │   │ │ NORMAL Queue        │ │ │ │
│  │  │ │     "assign",       │ │   │ │ LOW Queue           │ │ │ │
│  │  │ │   "service_type":   │ │   │ └─────────────────────┘ │ │ │
│  │  │ │     "skilled_nursing"│ │   └─────────────────────────┘ │ │
│  │  │ │ }                   │ │                               │ │
│  │  │ └─────────────────────┘ │                               │ │
│  │  └─────────────────────────┘                               │ │
│  │              │                              │               │ │
│  │              ▼                              ▼               │ │
│  │  ┌─────────────────────────────────────────────────────────┐ │ │
│  │  │            SCHEDULER SERVICE                            │ │ │
│  │  │  ┌─────────────────────────────────────────────────────┐ │ │ │
│  │  │  │ • Process immediate requests                        │ │ │ │
│  │  │  │ • Handle background events                          │ │ │ │
│  │  │  │ • Coordinate solver execution                       │ │ │ │
│  │  │  │ • Manage warm solver state                          │ │ │ │
│  │  │  └─────────────────────────────────────────────────────┘ │ │ │
│  │  └─────────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 7.2 Service Client System

````python
class ServiceClientFactory:
    """Factory for creating service clients with mock/production modes."""

    @staticmethod
    def create_staff_client(use_mock: bool = False) -> StaffServiceClient:
        if use_mock:
            return MockStaffServiceClient()
        return StaffServiceClient(ServiceConfig(
            base_url="https://api.careaxl.com/staff",
            api_key=os.getenv("STAFF_API_KEY")
        ))

    @staticmethod
    def create_patient_client(use_mock: bool = False) -> PatientServiceClient:
        if use_mock:
            return MockPatientServiceClient()
        return PatientServiceClient(ServiceConfig(
            base_url="https://api.careaxl.com/patients",
            api_key=os.getenv("PATIENT_API_KEY")
        ))
````

### 7.3 Event System Integration

````python
class EventType(Enum):
    """Event types for the scheduling system."""
    # Appointment events
    APPOINTMENT_CREATED = "appointment.created"
    APPOINTMENT_UPDATED = "appointment.updated"
    APPOINTMENT_CANCELLED = "appointment.cancelled"
    APPOINTMENT_ASSIGNED = "appointment.assigned"

    # Provider events
    PROVIDER_AVAILABILITY_CHANGED = "provider.availability.changed"
    PROVIDER_SCHEDULE_UPDATED = "provider.schedule.updated"

    # Optimization events
    SCHEDULE_OPTIMIZATION_REQUESTED = "schedule.optimization.requested"
    SCHEDULE_OPTIMIZATION_COMPLETED = "schedule.optimization.completed"

class EventPriority(Enum):
    """Event priority levels for queue routing."""
    CRITICAL = "critical"  # Immediate processing required
    HIGH = "high"         # Process within minutes
    NORMAL = "normal"     # Process within hours
    LOW = "low"          # Process when resources available
````

### 7.4 Event Processing Flow

```
┌─────────────────────────────────────────────────────────────┐
│                   EVENT PROCESSING FLOW                    │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  TYPICAL SCENARIO: Provider Availability Change             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  1. EXTERNAL SYSTEM EVENT                               │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ Staff Service: Provider availability updated    │ │ │
│  │     │ Event: PROVIDER_AVAILABILITY_CHANGED            │ │ │
│  │     │ Priority: HIGH                                  │ │ │
│  │     │ Data: {                                         │ │ │
│  │     │   provider_id: "PROV001",                       │ │ │
│  │     │   affected_dates: ["2024-07-01", "2024-07-02"] │ │ │
│  │     │ }                                               │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  2. EVENT HANDLER PROCESSING                            │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ async def handle_provider_change(event):        │ │ │
│  │     │     # Identify affected appointments            │ │ │
│  │     │     affected = find_affected_appointments(      │ │ │
│  │     │         event.data['provider_id'],              │ │ │
│  │     │         event.data['affected_dates']            │ │ │
│  │     │     )                                           │ │ │
│  │     │                                                 │ │ │
│  │     │     # Trigger incremental re-optimization       │ │ │
│  │     │     await trigger_optimization(                 │ │ │
│  │     │         optimization_type='incremental',        │ │ │
│  │     │         affected_appointments=affected          │ │ │
│  │     │     )                                           │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  3. OPTIMIZATION EXECUTION                              │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ • Load warm solver state                        │ │ │
│  │     │ • Update provider availability facts            │ │ │
│  │     │ • Re-optimize affected assignments              │ │ │
│  │     │ • Publish optimization results                  │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  │                              │                          │ │
│  │                              ▼                          │ │
│  │  4. RESULT PROPAGATION                                  │ │
│  │     ┌─────────────────────────────────────────────────┐ │ │
│  │     │ • Update external systems                       │ │ │
│  │     │ • Send provider notifications                   │ │ │
│  │     │ • Log optimization metrics                      │ │ │
│  │     │ • Publish completion events                     │ │ │
│  │     └─────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 8. Daily Solver Operations

### 8.1 Daily Execution Schedule

```
┌─────────────────────────────────────────────────────────────┐
│                   DAILY SOLVER EXECUTION                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  DAILY SCHEDULE                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  02:00 AM - Assignment Job (Stage 1)                    │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ • Process new appointment requests                  │ │ │
│  │  │ • Assign providers and dates                        │ │ │
│  │  │ • Optimize for rolling window (configurable days)   │ │ │
│  │  │ • Default: 7 days from current date                 │ │ │
│  │  │ • Configurable via `rolling_window_days` setting    │ │ │
│  │  │ • Output: Provider/date assignments                 │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  06:00 AM - Day Plan Job (Stage 2)                      │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ • Load today's assignments from Stage 1             │ │ │
│  │  │ • Assign specific time slots                        │ │ │
│  │  │ • Optimize visit orders and routes                  │ │ │
│  │  │ • Output: Complete daily schedules                  │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 8.2 Time Slot Assignment Process

````python
def _solve_time_assignment_problem(self, day_schedule: DaySchedule) -> DaySchedule:
    """Solve the time slot assignment problem using Timefold."""
    logger.info(f"Solving time assignment problem with {len(day_schedule.time_assignments)} appointments")

    # Create solver configuration
    solver_config = SolverConfig(
        solution_class=DaySchedule,
        entity_class_list=[TimeSlotAssignment],
        score_director_factory_config=ScoreDirectorFactoryConfig(
            constraint_provider_function=define_day_constraints
        ),
        termination_config=TerminationConfig(
            spent_limit=Duration(seconds=60)  # Shorter timeout for daily planning
        )
    )

    # Create and run solver
    solver_factory = SolverFactory.create(solver_config)
    solver = solver_factory.build_solver()
    solution = solver.solve(day_schedule)

    return solution
````

### 8.3 Visit Order Optimization

```
┌─────────────────────────────────────────────────────────────┐
│                 VISIT ORDER OPTIMIZATION                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  OPTIMIZATION FACTORS                                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  GEOGRAPHIC OPTIMIZATION                                │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Algorithm: Nearest Neighbor with Constraints        │ │ │
│  │  │                                                     │ │ │
│  │  │ 1. Start with timed appointments (fixed)            │ │ │
│  │  │ 2. For each remaining appointment:                  │ │ │
│  │  │    ├─ Calculate travel time from current location   │ │ │
│  │  │    ├─ Consider appointment duration                 │ │ │
│  │  │    ├─ Check time slot availability                  │ │ │
│  │  │    └─ Select nearest feasible appointment           │ │ │
│  │  │ 3. Repeat until all appointments scheduled          │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  HEALTHCARE TASK SEQUENCING                             │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Clinical workflow optimization:                     │ │ │
│  │  │ ├─ Medication administration early in day           │ │ │
│  │  │ ├─ Assessments before treatments                    │ │ │
│  │  │ ├─ Wound care after assessments                     │ │ │
│  │  │ ├─ IV therapy in controlled time windows            │ │ │
│  │  │ └─ Follow-up visits after primary care             │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  │                                                         │ │
│  │  BREAK TIME MANAGEMENT                                  │ │
│  │  ┌─────────────────────────────────────────────────────┐ │ │
│  │  │ Break requirements:                                 │ │ │
│  │  │ ├─ Lunch break: 30-60 minutes around midday        │ │ │
│  │  │ ├─ Short breaks: 15 minutes every 3 hours          │ │ │
│  │  │ ├─ Travel buffer: 5-10 minutes between appointments │ │ │
│  │  │ └─ Documentation time: 10 minutes after each visit │ │ │
│  │  └─────────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 8.4 Route Planning and Travel Time

````python
def calculate_travel_time(from_location, to_location, departure_time=None):
    """Calculate travel time between locations."""
    # Basic model (fallback)
    distance = haversine_distance(from_location, to_location)

    # Determine area type and speed
    if is_urban_area(from_location):
        speed = 25  # mph in urban areas
    elif is_suburban_area(from_location):
        speed = 35  # mph in suburban areas
    else:
        speed = 45  # mph in rural areas

    # Apply time-of-day factors
    if departure_time and is_rush_hour(departure_time):
        speed *= 0.6  # 40% slower during rush hour

    return (distance / speed) * 60  # minutes
````

---

## 9. Adding New Constraints

### 9.1 How to Add New Constraints

This section provides a step-by-step guide for adding new constraints to the scheduling engine. The process involves understanding the constraint lifecycle, following naming conventions, and integrating with the configuration system.

#### 9.1.1 Constraint Development Process

**Step 1: Identify the Constraint Type**
- **Assignment Stage**: Constraints that operate on `AppointmentAssignment` entities
- **Day Plan Stage**: Constraints that operate on `TimeSlotAssignment` entities
- **Hard vs Soft**: Determine if the constraint is mandatory (hard) or optimization goal (soft)

**Step 2: Choose the Constraint File**
- Assignment constraints: `src/constraints/cXXX_asgn_[constraint_name].py`
- Day plan constraints: `src/constraints/cXXX_schd_[constraint_name].py`
- Use the next available constraint number (C017, C018, etc.)

**Step 3: Follow the Constraint Template**
```python
@with_config()  # Uses default program type from scheduler.yml
def constraint_name(factory: ConstraintFactory,
                   core_config=None,
                   service_config=None,
                   **kwargs) -> Constraint:
    """Brief description of what this constraint does."""
    
    # Handle Timefold validation calls
    if factory is None:
        return factory.for_each(EntityType).filter(
            lambda x: False).as_constraint("no_op_constraint")
    
    # Check feature toggle from core configuration
    enable_feature = core_config.get('enable_feature_name', True)
    if not enable_feature:
        return factory.for_each(EntityType).filter(
            lambda x: False).as_constraint("feature_disabled")
    
    # Constraint logic here
    return (
        factory.for_each(EntityType)
        .filter(condition_function)
        .penalize(HardSoftScore.ONE_HARD, penalty_function)
        .as_constraint("Human readable constraint name")
    )
```

**Step 4: Register the Constraint**
- Add the constraint function to the appropriate coordinator file:
  - Assignment: `src/constraints/assignment_constraints.py`
  - Day Plan: `src/constraints/day_constraints.py`
- Import the new constraint function
- Add it to the constraints list

**Step 5: Add Configuration Support**
- Add feature toggle to `config/scheduler.yml`
- Add program-specific settings to program config files
- Update configuration documentation

**Step 6: Create Test Data**
- Add mock data for testing the constraint
- Create test scenarios in `src/data/scenarios/`
- Include edge cases and validation scenarios

#### 9.1.2 Constraint Naming Conventions

**File Naming:**
- Format: `cXXX_[stage]_[constraint_name].py`
- Examples:
  - `c017_asgn_provider_experience_level_requirements.py`
  - `c018_schd_medication_administration_time_windows.py`

**Function Naming:**
- Use descriptive, lowercase names with underscores
- Examples:
  - `provider_experience_level_requirements`
  - `medication_administration_time_windows`

**Constraint Names:**
- Use human-readable names in `.as_constraint()` calls
- Examples:
  - `"Provider experience level requirements"`
  - `"Medication administration time windows"`

#### 9.1.3 Configuration Integration

**Core Configuration (scheduler.yml):**
```yaml
# Feature toggles for new constraints
enable_new_constraint: true
new_constraint_settings:
  penalty_weight: 1
  bonus_weight: 30
```

**Program Configuration (program.yml):**
```yaml
# Program-specific settings
new_constraint:
  program_specific_setting: "value"
  threshold: 5
```

#### 9.1.4 Testing Guidelines

**Unit Testing:**
- Test constraint logic with mock data
- Verify penalty/reward calculations
- Test edge cases and boundary conditions

**Integration Testing:**
- Test with real solver execution
- Verify constraint interaction with other constraints
- Test configuration toggle behavior

**Scenario Testing:**
- Create test scenarios in `src/data/scenarios/`
- Include positive and negative test cases
- Test with different program types

### 9.2 Real-World Example: Provider Experience Level Requirements

**Scenario**: A new nurse with 6 months experience is available for assignment, but a high-risk patient with complex wound care needs requires senior-level expertise. The system must ensure patient safety by matching experience levels with case complexity.

**Business Rule**: New/inexperienced providers cannot be assigned to high-risk patients or complex cases that require senior-level expertise. The system should prioritize experienced providers for complex cases while allowing newer providers to handle routine care.

### 9.2 Assignment Stage Constraint Implementation

#### 9.2.1 Provider Experience Level Requirements

**Key Constraint Logic:**
```python
@with_config()
def provider_experience_level_requirements(factory: ConstraintFactory,
                                    core_config=None,
                                    service_config=None,
                                    **kwargs) -> Constraint:
    """Ensure providers have appropriate experience level for case complexity."""
    
    # Check feature toggle
    enable_experience_check = core_config.get('enable_experience_validation', True)
    if not enable_experience_check:
        return factory.for_each(AppointmentAssignment).filter(
            lambda x: False).as_constraint("experience_validation_disabled")

    def check_experience_mismatch(assignment):
        """Check if provider has sufficient experience for case complexity."""
        case_complexity = get_case_complexity(appointment)
        provider_experience = get_provider_experience_level(provider)
        return not has_sufficient_experience(provider_experience, case_complexity)

    def calculate_experience_penalty(assignment):
        """Calculate penalty based on experience gap."""
        experience_gap = case_complexity - provider_experience
        if experience_gap >= 3: return 5  # Critical mismatch
        elif experience_gap == 2: return 3  # Significant mismatch
        elif experience_gap == 1: return 2  # Minor mismatch
        else: return 1  # No mismatch

    return (
        factory.for_each(AppointmentAssignment)
        .filter(check_experience_mismatch)
        .penalize(HardSoftScore.ONE_HARD, calculate_experience_penalty)
        .as_constraint("Provider experience level requirements")
    )
```

**Helper Functions:**
```python
def get_case_complexity(appointment: AppointmentData) -> int:
    """Determine case complexity level (1-5, where 5 is most complex)."""
    complexity_factors = {
        "wound_care": 3,
        "medication_administration": 2,
        "diabetes_management": 4,
        "dementia_care": 5,
        "ventilator_management": 5
    }
    
    base_complexity = max([complexity_factors.get(skill, 1) 
                          for skill in appointment.required_skills])
    risk_adjustment = len(get_patient_risk_factors(appointment.patient_id)) * 0.5
    return min(5, int(base_complexity + risk_adjustment))

def has_sufficient_experience(provider_level: int, case_complexity: int) -> bool:
    """Check if provider has sufficient experience for case complexity."""
    return provider_level >= case_complexity
```

### 9.3 Assignment Stage Constraint Implementation (Language/Cultural Matching)

**Real-world scenario**: Mrs. Rodriguez speaks only Spanish and has cultural preferences for female caregivers. The system must match her with providers who can communicate effectively and respect her cultural care preferences.

**Key Constraint Logic:**
````python
@with_config()
def provider_language_cultural_matching(factory: ConstraintFactory,
                                      core_config=None,
                                      service_config=None,
                                      **kwargs) -> Constraint:
    """Match patients with providers who can communicate effectively and respect cultural preferences."""
    
    # Check feature toggle
    enable_language_matching = core_config.get('enable_language_matching', True)
    if not enable_language_matching:
        return factory.for_each(AppointmentAssignment).filter(
            lambda x: False).as_constraint("language_matching_disabled")

    def calculate_matching_bonus(assignment):
        """Calculate bonus for good language/cultural matches."""
        provider = assignment.provider
        patient_id = assignment.appointment_data.patient_id
        patient_preferences = get_patient_language_cultural_preferences(patient_id)

        bonus = 0
        if has_language_match(provider, patient_preferences['languages']):
            bonus += 30
        if has_cultural_match(provider, patient_preferences['cultural_preferences']):
            bonus += 20
        if is_native_speaker(provider, patient_preferences['primary_language']):
            bonus += 15
        return bonus

    return (
        factory.for_each(AppointmentAssignment)
        .filter(lambda assignment: (
            assignment.provider is not None and
            assignment.appointment_data is not None
        ))
        .reward(HardSoftScore.ONE_SOFT, calculate_matching_bonus)
        .as_constraint("Provider language/cultural matching")
    )
````

**Helper Functions:**
````python
def has_language_match(provider: Provider, required_languages: List[str]) -> bool:
    """Check if provider speaks any of the required languages."""
    provider_languages = get_provider_languages(provider)
    return any(lang in provider_languages for lang in required_languages)

def has_cultural_match(provider: Provider, cultural_preferences: Dict[str, Any]) -> bool:
    """Check if provider meets cultural preferences."""
    provider_profile = get_provider_cultural_profile(provider)
    
    # Check gender preference
    if cultural_preferences.get('gender_preference') != 'no_preference':
        if provider_profile['gender'] != cultural_preferences['gender_preference']:
            return False
    
    # Check cultural sensitivity training
    required_sensitivity = cultural_preferences.get('cultural_sensitivity', [])
    if required_sensitivity:
        provider_sensitivity = provider_profile.get('cultural_sensitivity_training', [])
        if not any(culture in provider_sensitivity for culture in required_sensitivity):
            return False
    
    return True
````

### 9.4 Day Plan Stage Constraint Implementation (Medication Timing)

**Real-world scenario**: Mr. Davis requires insulin administration 30 minutes before each meal. The system must schedule his medication visits within specific time windows relative to his meal times to ensure proper medication effectiveness and patient safety.

**Key Constraint Logic:**
````python
@with_config()
def medication_administration_time_windows(factory: ConstraintFactory,
                                         core_config=None,
                                         service_config=None,
                                         **kwargs) -> Constraint:
    """Ensure medication administration visits occur within required time windows."""
    
    # Check feature toggle
    enable_medication_timing = core_config.get('enable_medication_timing_windows', True)
    if not enable_medication_timing:
        return factory.for_each(TimeSlotAssignment).filter(
            lambda x: False).as_constraint("medication_timing_disabled")

    def check_medication_timing_violation(assignment):
        """Check if medication visit is scheduled within required time window."""
        appointment = assignment.scheduled_appointment
        if not is_medication_administration_visit(appointment):
            return False

        patient_id = appointment.appointment_data.patient_id
        meal_schedule = get_patient_meal_schedule(patient_id, appointment.date)
        
        # Check if visit time is within required window for any meal
        for meal_time in meal_schedule:
            if is_within_medication_window(assignment.time_slot, meal_time):
                return False  # No violation - timing is correct
        return True  # Violation - not within any medication window

    def calculate_timing_penalty(assignment):
        """Calculate penalty based on how far outside the time window."""
        appointment = assignment.scheduled_appointment
        patient_id = appointment.appointment_data.patient_id
        meal_schedule = get_patient_meal_schedule(patient_id, appointment.date)

        # Find closest meal time and calculate deviation
        min_deviation = min([
            abs((assignment.time_slot.hour * 60 + assignment.time_slot.minute) - 
                (meal_time.hour * 60 + meal_time.minute))
            for meal_time in meal_schedule
        ])

        # Higher penalty for greater deviation from meal time
        if min_deviation <= 15: return 1      # Within 15 minutes
        elif min_deviation <= 30: return 2    # Within 30 minutes
        elif min_deviation <= 60: return 3    # Within 1 hour
        else: return 5                        # More than 1 hour

    return (
        factory.for_each(TimeSlotAssignment)
        .filter(check_medication_timing_violation)
        .penalize(HardSoftScore.ONE_HARD, calculate_timing_penalty)
        .as_constraint("Medication administration time windows")
    )
````

**Helper Functions:**
````python
def is_medication_administration_visit(appointment: ScheduledAppointment) -> bool:
    """Check if appointment involves medication administration."""
    return "medication_administration" in appointment.appointment_data.required_skills

def is_within_medication_window(visit_time: time, meal_time: time) -> bool:
    """Check if visit time is within medication administration window."""
    # Medication should be given 30 minutes before meal
    medication_window_start = subtract_minutes(meal_time, 45)  # 45 min before meal
    medication_window_end = subtract_minutes(meal_time, 15)    # 15 min before meal
    return medication_window_start <= visit_time <= medication_window_end
````

### 9.5 Day Plan Stage Constraint Implementation (Family Caregiver Availability)

**Real-world scenario**: Mrs. Johnson requires assistance with bathing and mobility exercises. Her daughter Sarah works from 9 AM to 5 PM but is available evenings and weekends. The system must schedule visits requiring family assistance during times when Sarah can be present.

**Key Constraint Logic:**
````python
@with_config()
def family_caregiver_availability(factory: ConstraintFactory,
                                core_config=None,
                                service_config=None,
                                **kwargs) -> Constraint:
    """Schedule visits requiring family assistance during family caregiver availability."""
    
    # Check feature toggle
    enable_family_caregiver_check = core_config.get('enable_family_caregiver_availability', True)
    if not enable_family_caregiver_check:
        return factory.for_each(TimeSlotAssignment).filter(
            lambda x: False).as_constraint("family_caregiver_check_disabled")

    def calculate_family_availability_bonus(assignment):
        """Calculate bonus for scheduling during family caregiver availability."""
        appointment = assignment.scheduled_appointment
        if not requires_family_assistance(appointment):
            return 0

        patient_id = appointment.appointment_data.patient_id
        family_availability = get_family_caregiver_availability(patient_id, appointment.date)
        
        visit_time = assignment.time_slot
        visit_day = appointment.date.weekday()

        # Check if scheduled during family availability
        for availability_window in family_availability:
            if (availability_window['day_of_week'] == visit_day and
                availability_window['start_time'] <= visit_time <= availability_window['end_time']):
                return 40  # Bonus for family availability
        return 0

    return (
        factory.for_each(TimeSlotAssignment)
        .filter(lambda assignment: (
            assignment.time_slot is not None and
            assignment.scheduled_appointment is not None
        ))
        .reward(HardSoftScore.ONE_SOFT, calculate_family_availability_bonus)
        .as_constraint("Family caregiver availability")
    )
````

**Helper Functions:**
````python
def requires_family_assistance(appointment: ScheduledAppointment) -> bool:
    """Check if appointment requires family caregiver assistance."""
    family_assistance_tasks = [
        "bathing_assistance", "mobility_exercises", "transfer_assistance",
        "cognitive_stimulation", "behavioral_management"
    ]
    return any(skill in family_assistance_tasks 
               for skill in appointment.appointment_data.required_skills)

def get_family_caregiver_availability(patient_id: str, target_date: date) -> List[Dict[str, Any]]:
    """Get family caregiver availability for the target date."""
    # In real implementation, this would query family caregiver database
    # Returns list of availability windows with day_of_week, start_time, end_time
    return []  # Mock implementation
````

### 9.6 Configuration Integration

````yaml
# Provider experience validation settings
enable_experience_validation: true
experience_level_thresholds:
  junior: 2  # years
  intermediate: 4  # years
  senior: 7  # years
  expert: 10  # years

# Language and cultural matching settings
enable_language_matching: true
enable_cultural_matching: true
language_matching_bonus: 30
cultural_matching_bonus: 20
native_speaker_bonus: 15

# Medication timing settings
enable_medication_timing_windows: true
medication_window_before_meal_minutes: 30
medication_window_tolerance_minutes: 15

# Family caregiver availability settings
enable_family_caregiver_availability: true
family_availability_bonus: 40
````



---

## 10. External System Integration

### 10.1 Integration Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                EXTERNAL SYSTEM INTEGRATION                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  API ENDPOINTS (Inbound)                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  POST /api/v1/schedule/assign                           │ │
│  │  ├─ Trigger assignment optimization                     │ │
│  │  └─ Response: assignment results and statistics         │ │
│  │                                                         │ │
│  │  POST /api/v1/schedule/dayplan                          │ │
│  │  ├─ Trigger day planning optimization                   │ │
│  │  └─ Response: time slot assignments and routes          │ │
│  │                                                         │ │
│  │  GET /api/v1/schedule/status                            │ │
│  │  └─ Response: solver state, queue status, metrics      │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  EXTERNAL SERVICE APIs (Outbound)                          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  Staff Service API                                      │ │
│  │  ├─ GET /api/staff/providers                            │ │
│  │  ├─ GET /api/staff/availability/{provider_id}           │ │
│  │  └─ PUT /api/staff/assignments                          │ │
│  │                                                         │ │
│  │  Patient Service API                                    │ │
│  │  ├─ GET /api/patients                                   │ │
│  │  ├─ GET /api/patients/{id}/location                     │ │
│  │  └─ GET /api/patients/{id}/preferences                  │ │
│  │                                                         │ │
│  │  Appointment Service API                                │ │
│  │  ├─ GET /api/appointments                               │ │
│  │  ├─ POST /api/appointments                              │ │
│  │  └─ PUT /api/appointments/{id}/schedule                 │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  EVENT-DRIVEN INTEGRATION                                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                                                         │ │
│  │  RabbitMQ Event Bus                                     │ │
│  │  ├─ CRITICAL Queue: Immediate processing required       │ │
│  │  ├─ HIGH Queue: Process within minutes                  │ │
│  │  ├─ NORMAL Queue: Process within hours                  │ │
│  │  └─ LOW Queue: Process when resources available         │ │
│  │                                                         │ │
│  │  Event Types                                            │ │
│  │  ├─ APPOINTMENT_CREATED/UPDATED/CANCELLED              │ │
│  │  ├─ PROVIDER_AVAILABILITY_CHANGED                      │ │
│  │  ├─ SCHEDULE_OPTIMIZATION_REQUESTED                    │ │
│  │  └─ SCHEDULE_OPTIMIZATION_COMPLETED                    │ │
│  │                                                         │ │
│  │  Event Processing                                       │ │
│  │  ├─ Asynchronous event handling                         │ │
│  │  ├─ Priority-based queue routing                        │ │
│  │  ├─ Incremental optimization triggers                   │ │
│  │  └─ Real-time status updates                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```





---

## 11. Warm Solver Performance Optimization

### 11.1 Overview

The warm solver feature provides performance optimization through state persistence and incremental updates. It enables faster startup times and better convergence by reusing previous solver states.

### 11.2 Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    WARM SOLVER ARCHITECTURE                │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  JOB EXECUTION                                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ 1. Check if warm solver enabled                         │ │
│  │ 2. Try to load cached state                             │ │
│  │ 3. If cache found:                                      │ │
│  │    ├─ Merge with new data                               │ │
│  │    ├─ Solve incrementally                               │ │
│  │    └─ Save updated state                                │ │
│  │ 4. If no cache:                                         │ │
│  │    ├─ Perform cold start                                │ │
│  │    └─ Save new state                                    │ │
│  └─────────────────────────────────────────────────────────┘ │
│                                                             │
│  STATE MANAGEMENT                                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ • Solver state extraction                               │ │
│  │ • Solution serialization                                │ │
│  │ • Metadata storage                                      │ │
│  │ • Cache expiration handling                             │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 11.3 Implementation Components

#### 11.3.1 WarmSolverManager

The `WarmSolverManager` class handles all aspects of state persistence:

````python
class WarmSolverManager:
    """Manages warm solver state persistence and restoration."""
    
    def save_solver_state(self, job_type: str, solver, solution, metadata: Dict[str, Any]) -> bool:
        """Save solver state and solution to cache."""
        
    def load_solver_state(self, job_type: str) -> Optional[Dict[str, Any]]:
        """Load cached solver state if available and fresh."""
        
    def clear_cache(self, job_type: Optional[str] = None):
        """Clear cache files."""
        
    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about cached states."""
````

#### 11.3.2 Job Integration

Both `AssignAppointmentJob` and `DayPlanJob` support warm solver mode:

````python
# Assignment Job with warm solver
assign_job = AssignAppointmentJob(warm_solver=True)
result = assign_job.run()

# Day Plan Job with warm solver  
day_plan_job = DayPlanJob(warm_solver=True)
result = day_plan_job.run(target_date)
````

#### 11.3.3 State Persistence

The warm solver persists:

1. **Solver State**: Current solver configuration and state
2. **Solution**: Best solution found so far
3. **Metadata**: Job information, timestamps, statistics
4. **Cache Info**: File sizes, ages, validation data

### 11.4 Usage Examples

#### 11.4.1 Command Line Usage

```bash
# Enable warm solver for assignment job
python scheduler.py --mode once --job assign --warm-solver

# Enable warm solver for day plan job
python scheduler.py --mode once --job dayplan --date 2024-07-01 --warm-solver

# Run as daemon with warm solver
python scheduler.py --mode daemon --warm-solver
```

#### 11.4.2 Programmatic Usage

```python
from assign_appointments import AssignAppointmentJob
from day_plan import DayPlanJob

# Create job with warm solver enabled
assign_job = AssignAppointmentJob(warm_solver=True)
day_plan_job = DayPlanJob(warm_solver=True)

# Run jobs (warm solver will be used automatically)
result = assign_job.run()
day_result = day_plan_job.run()
```

#### 11.4.3 Demo and Testing

```bash
# Run the warm solver demo
python examples/warm_solver_demo.py
```

The demo will:
1. Run cold start optimization (3 iterations)
2. Run warm solver optimization (3 iterations)
3. Compare performance improvements
4. Show cache information

### 11.5 Cache Management

#### 11.5.1 Cache Information

```python
from src.solver.warm_solver_manager import WarmSolverManager

# Get cache information
manager = WarmSolverManager()
cache_info = manager.get_cache_info()

print(f"Cache directory: {cache_info['cache_directory']}")
for job_type, info in cache_info['cached_jobs'].items():
    print(f"Job type: {job_type}")
    print(f"  Timestamp: {info['timestamp']}")
    print(f"  File size: {info['file_size']} bytes")
    print(f"  Age: {info['age_hours']:.1f} hours")
```

#### 11.5.2 Cache Files

The warm solver creates cache files in the configured directory:

```
cache/warm_solver/
├── assign_state.pkl    # Assignment job state
└── dayplan_state.pkl   # Day plan job state
```

#### 11.5.3 Cache Operations

```python
from src.solver.warm_solver_manager import WarmSolverManager

# Clear all cache
manager = WarmSolverManager()
manager.clear_cache()

# Clear specific job type
manager.clear_cache('assign')  # Clear assignment cache only
```

### 11.6 Performance Considerations

#### 11.6.1 Expected Improvements

- **First Run**: Similar to cold start (cache creation overhead)
- **Subsequent Runs**: 10-30% faster execution time
- **Convergence**: Better solution quality due to warm start
- **Memory Usage**: Slightly higher due to cached state

#### 11.6.2 Best Practices

1. **Enable for Production**: Use warm solver in production environments
2. **Monitor Cache Size**: Check cache directory size periodically
3. **Clear Old Cache**: Remove expired cache files if needed
4. **Test Incremental Updates**: Verify behavior with new appointments
5. **Backup Important States**: Consider backing up critical solver states

### 11.7 Incremental Updates

When new appointments are added:

1. **Load Cached State**: Retrieve previous solver state and solution
2. **Merge Data**: Combine new appointments with existing assignments
3. **Incremental Solve**: Start from cached state and optimize for new data
4. **Update Cache**: Save the improved solution

### 11.8 Error Handling

The warm solver includes robust error handling:

- **Cache Corruption**: Automatically removes corrupted cache files
- **Solver Failures**: Falls back to cold start on solver errors
- **State Incompatibility**: Handles version mismatches gracefully
- **Disk Issues**: Continues operation even with storage problems

### 11.9 Troubleshooting

#### 11.9.1 Common Issues

**Warm solver not working:**
- Check `warm_solver.enabled` in configuration
- Verify cache directory permissions
- Check logs for error messages

**Performance not improving:**
- Ensure multiple runs with same data
- Check cache file sizes and ages
- Verify incremental update logic

**Cache corruption:**
- Clear cache directory: `manager.clear_cache()`
- Check disk space and permissions
- Review error logs for corruption causes

#### 11.9.2 Debug Mode

Enable debug logging to see warm solver operations:

```python
import logging
logging.getLogger('src.solver.warm_solver_manager').setLevel(logging.DEBUG)
```

---

## Conclusion

The CareAXL Scheduling Engine provides a robust, scalable solution for healthcare appointment optimization through:

**Key Architecture Benefits:**
- **Two-Stage Design**: Separates strategic (provider/date) from tactical (time/route) decisions
- **Constraint-Driven**: Flexible business rule implementation with configuration injection
- **Hybrid Integration**: Combines synchronous APIs with asynchronous event processing
- **Warm Solver**: Optimized performance through state persistence

**Maintenance and Extension:**
- Clear patterns for adding new constraints
- Hierarchical configuration management
- Comprehensive testing framework
- Professional documentation and examples
- Warm solver state management and caching

**Performance Optimization:**
- Warm solver with state persistence and incremental updates
- 10-30% performance improvement on subsequent runs
- Automatic cache management and error recovery
- Backward compatibility with opt-in feature enablement

This system is designed for production healthcare environments where reliability, performance, and compliance are critical requirements.


