# Main Scheduler Configuration
# This file contains global settings for the appointment scheduling system

# Rolling window configuration
rolling_window_days: 7
max_solving_time_seconds: 300

# Default program type for constraints (when not specified)
default_program_type: "skilled_nursing"

# Warm Solver Configuration
warm_solver:
  enabled: false  # Default to false for backward compatibility
  cache_directory: "cache/warm_solver"
  max_cache_age_hours: 24
  incremental_updates: true
  preserve_solver_state: true

# Configuration paths
config_folder: "config"
log_level: "INFO"

# =============================================================================
# GLOBAL AVAILABILITY CONFIGURATION
# =============================================================================

# Organization-wide availability settings
global_availability:
  # Organization-wide blackout periods
  blackout_periods:
    - name: "Christmas Break"
      start_date: "12-24"
      end_date: "12-26"
      reason: "organization_holiday"
      severity: "hard"
      description: "Organization-wide Christmas holiday break"

    - name: "New Year Break"
      start_date: "12-31"
      end_date: "01-02"
      reason: "organization_holiday"
      severity: "hard"
      description: "Organization-wide New Year holiday break"

    - name: "Summer Maintenance"
      start_date: "07-01"
      end_date: "07-15"
      reason: "system_maintenance"
      severity: "soft"
      description: "Annual system maintenance period"
  
  # Organization-wide holidays (US Federal Holidays)
  holidays:
    - name: "New Year's Day"
      date: "01-01"
      type: "federal"
      description: "US Federal Holiday"

    - name: "Martin Luther King Jr. Day"
      date: "01-15"
      type: "federal"
      description: "US Federal Holiday"

    - name: "Presidents' Day"
      date: "02-19"
      type: "federal"
      description: "US Federal Holiday"

    - name: "Memorial Day"
      date: "05-27"
      type: "federal"
      description: "US Federal Holiday"

    - name: "Independence Day"
      date: "07-04"
      type: "federal"
      description: "US Federal Holiday"

    - name: "Labor Day"
      date: "09-02"
      type: "federal"
      description: "US Federal Holiday"

    - name: "Columbus Day"
      date: "10-14"
      type: "federal"
      description: "US Federal Holiday"

    - name: "Veterans Day"
      date: "11-11"
      type: "federal"
      description: "US Federal Holiday"

    - name: "Thanksgiving Day"
      date: "11-28"
      type: "federal"
      description: "US Federal Holiday"

    - name: "Christmas Day"
      date: "12-25"
      type: "federal"
      description: "US Federal Holiday"
  
  # Default weekend coverage policy
  default_weekend_coverage: false
  weekend_coverage_penalty: 2

# =============================================================================
# FEATURE TOGGLES
# =============================================================================

# Assignment Stage Feature Toggles
# (Affect provider/date assignment)
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: true
enable_provider_capacity_management: true

# Schedule/Day Planning Stage Feature Toggles
# (Affect time slot and daily schedule optimization)
enable_healthcare_task_sequencing: true
enable_travel_time_optimization: true
enable_break_time_management: true
enable_route_optimization: true

# =============================================================================
# ADVANCED TRAFFIC INTEGRATION (ENTERPRISE FEATURE)
# =============================================================================

# Enable enhanced traffic considerations with real-time APIs
enable_advanced_traffic_integration: true

# Traffic API Configuration
traffic_integration:
  # Google Maps Routes API for real-time traffic and routing
  google_maps:
    enabled: true
    api_key: "AIzaSyC7z-cWYB_Ylcq4H_t7ok5-gmPg6dNbAE4"
    base_url: "https://routes.googleapis.com/directions/v2"  # New Routes API endpoint
    use_routes_api: true  # Use new Routes API instead of legacy Directions API

  # Google Weather API for weather-aware routing
  google_weather:
    enabled: true
    api_key: "AIzaSyC7z-cWYB_Ylcq4H_t7ok5-gmPg6dNbAE4"  # Same API key can be used
    base_url: "https://weather.googleapis.com/v1"

  # Weather API for traffic impact (OpenWeatherMap as fallback)
  weather:
    enabled: true
    api_key: "15a581895eec115395f1e13ba56c6b09"  # Set your weather API key here
    provider: "openweathermap"  # openweathermap, accuweather, etc.
    base_url: "https://api.openweathermap.org/data/2.5"

  # Historical traffic data
  historical:
    enabled: false
    data_source: "local"  # local, external_api, database
    cache_duration_hours: 24

  # Traffic pattern analysis
  patterns:
    enabled: true
    time_of_day_factors:
      rush_hour_morning: 0.6  # 60% slower during morning rush (7-9 AM)
      rush_hour_evening: 0.7  # 70% slower during evening rush (5-7 PM)
      weekend_factor: 1.2     # 20% faster on weekends
      holiday_factor: 1.3     # 30% faster on holidays

  # Fallback settings
  fallback:
    use_simplified_model: true  # Use basic urban/rural model if APIs fail
    cache_timeout_minutes: 15   # Cache API responses for 15 minutes
    retry_attempts: 3           # Number of API retry attempts

# =============================================================================
# BASIC TRAFFIC MODEL CONFIGURATION
# =============================================================================

# Configurable urban cities and speed factors
traffic_model:
  # List of cities considered urban (with traffic congestion)
  urban_cities:
    - "New York"
    - "Los Angeles"
    - "Chicago"
    - "Houston"
    - "Phoenix"
    - "Philadelphia"
    - "San Antonio"
    - "San Diego"
    - "Dallas"
    - "San Jose"
    - "Austin"
    - "Jacksonville"
    - "Fort Worth"
    - "Columbus"
    - "Charlotte"
    - "San Francisco"
    - "Indianapolis"
    - "Seattle"
    - "Denver"
    - "Washington"
    - "Boston"
    - "El Paso"
    - "Nashville"
    - "Detroit"
    - "Oklahoma City"
    - "Portland"
    - "Las Vegas"
    - "Memphis"
    - "Louisville"
    - "Baltimore"
    - "Milwaukee"
    - "Albuquerque"
    - "Tucson"
    - "Fresno"
    - "Sacramento"
    - "Mesa"
    - "Kansas City"
    - "Atlanta"
    - "Long Beach"
    - "Colorado Springs"
    - "Raleigh"
    - "Miami"
    - "Virginia Beach"
    - "Omaha"
    - "Oakland"
    - "Minneapolis"
    - "Tampa"
    - "Tulsa"
    - "Arlington"
    - "New Orleans"
    - "Wichita"
    - "Cleveland"
    - "Bakersfield"
    - "Aurora"
    - "Anaheim"
    - "Honolulu"
    - "Santa Ana"
    - "Corpus Christi"
    - "Riverside"
    - "Lexington"
    - "Stockton"
    - "Henderson"
    - "Saint Paul"
    - "St. Louis"
    - "Cincinnati"
    - "Pittsburgh"
    - "Greensboro"
    - "Anchorage"
    - "Plano"
    - "Orlando"
    - "Irvine"
    - "Newark"
    - "Durham"
    - "Chula Vista"
    - "Toledo"
    - "Fort Wayne"
    - "St. Petersburg"
    - "Laredo"
    - "Jersey City"
    - "Chandler"
    - "Madison"
    - "Lubbock"
    - "Scottsdale"
    - "Reno"
    - "Buffalo"
    - "Gilbert"
    - "Glendale"
    - "North Las Vegas"
    - "Winston-Salem"
    - "Chesapeake"
    - "Norfolk"
    - "Fremont"
    - "Garland"
    - "Irving"
    - "Hialeah"
    - "Richmond"
    - "Boise"
    - "Spokane"
    - "Baton Rouge"
    - "Tacoma"
    - "San Bernardino"
    - "Grand Rapids"
    - "Huntsville"
    - "Salt Lake City"
    - "Frisco"
    - "Yonkers"
    - "Amarillo"
    - "Glendale"
    - "McKinney"
    - "Montgomery"
    - "Aurora"
    - "Des Moines"
    - "Modesto"
    - "Fayetteville"
    - "Shreveport"
    - "Rochester"
    - "Akron"
    - "Yuma"
    - "Lubbock"
    - "Oxnard"
    - "Fontana"
    - "Columbus"
    - "Montgomery"
    - "Moreno Valley"
    - "Huntington Beach"
    - "Port St. Lucie"
    - "Grand Prairie"
    - "Tallahassee"
    - "Overland Park"
    - "Tempe"
    - "McKinney"
    - "Mobile"
    - "Cape Coral"
    - "Shreveport"
    - "Frisco"
    - "Oakland Park"
    - "Huntington Beach"
    - "Yonkers"
    - "Glendale"
    - "Aurora"
    - "Fayetteville"
    - "Modesto"
    - "Rochester"
    - "Des Moines"
    - "Montgomery"
    - "Amarillo"
    - "Salt Lake City"
    - "Huntsville"
    - "Grand Rapids"
    - "Tacoma"
    - "Baton Rouge"
    - "Spokane"
    - "Boise"
    - "Richmond"
    - "Hialeah"
    - "Irving"
    - "Garland"
    - "Fremont"
    - "Norfolk"
    - "Chesapeake"
    - "Winston-Salem"
    - "North Las Vegas"
    - "Glendale"
    - "Gilbert"
    - "Buffalo"
    - "Reno"
    - "Scottsdale"
    - "Lubbock"
    - "Madison"
    - "Chandler"
    - "Jersey City"
    - "Laredo"
    - "St. Petersburg"
    - "Fort Wayne"
    - "Toledo"
    - "Chula Vista"
    - "Durham"
    - "Newark"
    - "Irvine"
    - "Orlando"
    - "Plano"
    - "Anchorage"
    - "Greensboro"
    - "Pittsburgh"
    - "Cincinnati"
    - "St. Louis"
    - "Saint Paul"
    - "Henderson"
    - "Stockton"
    - "Lexington"
    - "Riverside"
    - "Corpus Christi"
    - "Santa Ana"
    - "Honolulu"
    - "Anaheim"
    - "Aurora"
    - "Bakersfield"
    - "Cleveland"
    - "Wichita"
    - "New Orleans"
    - "Arlington"
    - "Tulsa"
    - "Tampa"
    - "Minneapolis"
    - "Oakland"
    - "Omaha"
    - "Virginia Beach"
    - "Miami"
    - "Raleigh"
    - "Colorado Springs"
    - "Long Beach"
    - "Atlanta"
    - "Kansas City"
    - "Mesa"
    - "Sacramento"
    - "Fresno"
    - "Tucson"
    - "Albuquerque"
    - "Milwaukee"
    - "Baltimore"
    - "Louisville"
    - "Memphis"
    - "Las Vegas"
    - "Portland"
    - "Oklahoma City"
    - "Detroit"
    - "Nashville"
    - "El Paso"
    - "Boston"
    - "Washington"
    - "Denver"
    - "Seattle"
    - "Indianapolis"
    - "San Francisco"
    - "Charlotte"
    - "Columbus"
    - "Fort Worth"
    - "Jacksonville"
    - "Austin"
    - "San Jose"
    - "Dallas"
    - "San Diego"
    - "San Antonio"
    - "Philadelphia"
    - "Phoenix"
    - "Houston"
    - "Chicago"
    - "Los Angeles"
    - "New York"
  
  # Speed factors for different area types (miles per hour)
  speed_factors:
    urban_speed_mph: 25      # Average speed in urban areas with traffic
    suburban_speed_mph: 35   # Average speed in suburban areas
    rural_speed_mph: 45      # Average speed in rural areas
    highway_speed_mph: 65    # Average speed on highways

  # Time-based speed adjustments (multipliers)
  time_adjustments:
    rush_hour_morning_multiplier: 0.6   # 60% of normal speed during morning rush (7-9 AM)
    rush_hour_evening_multiplier: 0.7   # 70% of normal speed during evening rush (5-7 PM)
    weekend_multiplier: 1.2             # 20% faster on weekends
    holiday_multiplier: 1.3             # 30% faster on holidays
    night_multiplier: 1.4               # 40% faster at night (10 PM - 6 AM) 