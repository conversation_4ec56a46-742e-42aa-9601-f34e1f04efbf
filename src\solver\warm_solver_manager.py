"""
Warm Solver Manager for healthcare scheduling optimization.

This module manages warm solver state persistence and restoration to enable
faster startup and better convergence by reusing previous solver states.
"""

import pickle
import json
import os
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Union
from pathlib import Path
from loguru import logger

from model.planning_models import AppointmentSchedule, DaySchedule


class WarmSolverManager:
    """Manages warm solver state persistence and restoration."""
    
    def __init__(self, cache_directory: str = "cache/warm_solver"):
        self.cache_directory = Path(cache_directory)
        self.cache_directory.mkdir(parents=True, exist_ok=True)
        logger.info(f"Warm solver manager initialized with cache directory: {self.cache_directory}")
    
    def save_solver_state(self, job_type: str, solver, solution: Union[AppointmentSchedule, DaySchedule], 
                         metadata: Dict[str, Any]) -> bool:
        """
        Save solver state and solution to cache.
        
        Args:
            job_type: Type of job ('assign' or 'dayplan')
            solver: Timefold solver instance
            solution: Current solution
            metadata: Additional metadata to cache
            
        Returns:
            bool: True if save was successful
        """
        try:
            timestamp = datetime.now().isoformat()
            state_data = {
                'timestamp': timestamp,
                'job_type': job_type,
                'solver_state': self._extract_solver_state(solver),
                'solution': solution,
                'metadata': metadata
            }
            
            cache_file = self.cache_directory / f"{job_type}_state.pkl"
            with open(cache_file, 'wb') as f:
                pickle.dump(state_data, f)
            
            logger.info(f"Saved warm solver state for {job_type} job")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save warm solver state: {e}")
            return False
    
    def load_solver_state(self, job_type: str) -> Optional[Dict[str, Any]]:
        """
        Load cached solver state if available and fresh.
        
        Args:
            job_type: Type of job ('assign' or 'dayplan')
            
        Returns:
            Dict containing cached state or None if not available/expired
        """
        cache_file = self.cache_directory / f"{job_type}_state.pkl"
        
        if not cache_file.exists():
            logger.debug(f"No cache file found for {job_type} job")
            return None
            
        # Check cache age
        if self._is_cache_expired(cache_file):
            logger.info(f"Cache expired for {job_type} job, removing old cache")
            cache_file.unlink()
            return None
            
        try:
            with open(cache_file, 'rb') as f:
                cached_state = pickle.load(f)
            logger.info(f"Loaded warm solver state for {job_type} job")
            return cached_state
            
        except Exception as e:
            logger.error(f"Failed to load warm solver state: {e}")
            # Remove corrupted cache file
            cache_file.unlink()
            return None
    
    def clear_cache(self, job_type: Optional[str] = None):
        """
        Clear cache files.
        
        Args:
            job_type: Specific job type to clear, or None to clear all
        """
        if job_type:
            cache_file = self.cache_directory / f"{job_type}_state.pkl"
            if cache_file.exists():
                cache_file.unlink()
                logger.info(f"Cleared cache for {job_type} job")
        else:
            for cache_file in self.cache_directory.glob("*_state.pkl"):
                cache_file.unlink()
            logger.info("Cleared all warm solver cache files")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about cached states."""
        cache_info = {
            'cache_directory': str(self.cache_directory),
            'cached_jobs': {}
        }
        
        for cache_file in self.cache_directory.glob("*_state.pkl"):
            job_type = cache_file.stem.replace('_state', '')
            try:
                with open(cache_file, 'rb') as f:
                    state_data = pickle.load(f)
                cache_info['cached_jobs'][job_type] = {
                    'timestamp': state_data.get('timestamp'),
                    'file_size': cache_file.stat().st_size,
                    'age_hours': self._get_cache_age_hours(cache_file)
                }
            except Exception as e:
                cache_info['cached_jobs'][job_type] = {'error': str(e)}
        
        return cache_info
    
    def _extract_solver_state(self, solver) -> Dict[str, Any]:
        """
        Extract relevant state from solver for caching.
        
        Args:
            solver: Timefold solver instance
            
        Returns:
            Dict containing solver state information
        """
        try:
            # Extract basic solver information
            state = {
                'solver_type': type(solver).__name__,
                'extraction_timestamp': datetime.now().isoformat()
            }
            
            # Try to extract solver-specific state if available
            if hasattr(solver, 'get_best_solution'):
                try:
                    state['best_solution'] = solver.get_best_solution()
                except Exception as e:
                    logger.debug(f"Could not extract best solution: {e}")
            
            if hasattr(solver, 'get_working_solution'):
                try:
                    state['working_solution'] = solver.get_working_solution()
                except Exception as e:
                    logger.debug(f"Could not extract working solution: {e}")
            
            if hasattr(solver, 'get_termination_status'):
                try:
                    state['termination_status'] = solver.get_termination_status()
                except Exception as e:
                    logger.debug(f"Could not extract termination status: {e}")
            
            return state
            
        except Exception as e:
            logger.error(f"Failed to extract solver state: {e}")
            return {'error': str(e)}
    
    def _is_cache_expired(self, cache_file: Path) -> bool:
        """
        Check if cache file is older than max age.
        
        Args:
            cache_file: Path to cache file
            
        Returns:
            bool: True if cache is expired
        """
        try:
            # Default max age is 24 hours, can be made configurable
            max_age = timedelta(hours=24)
            file_age = datetime.now() - datetime.fromtimestamp(cache_file.stat().st_mtime)
            return file_age > max_age
        except Exception as e:
            logger.error(f"Error checking cache age: {e}")
            return True  # Consider expired if we can't check
    
    def _get_cache_age_hours(self, cache_file: Path) -> float:
        """Get cache age in hours."""
        try:
            file_age = datetime.now() - datetime.fromtimestamp(cache_file.stat().st_mtime)
            return file_age.total_seconds() / 3600
        except Exception:
            return 0.0 