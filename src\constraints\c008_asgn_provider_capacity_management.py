"""
Provider Capacity Management Constraint (C008)

This constraint manages provider capacity and workload limits.
This is a SOFT constraint for optimization preferences.
"""

from datetime import date
from typing import TYPE_CHECKING, List

from timefold.solver.score import HardSoftScore, ConstraintFactory, Constraint

from model.domain import AppointmentData
from model.planning_models import AppointmentAssignment
from .base_constraints import with_config

if TYPE_CHECKING:
    from model.domain import Provider

# --- Public Utility Functions (moved from utils) ---
def get_provider_workload_for_date(provider: 'Provider', target_date, assignments) -> int:
    """
    Get the number of appointments assigned to a provider on a specific date.
    
    Args:
        provider: Provider object
        target_date: Date to check
        assignments: List of appointment assignments
    
    Returns:
        int: Number of appointments assigned to provider on the date
    """
    count = 0
    for assignment in assignments:
        if (assignment.provider == provider and
                assignment.assigned_date == target_date):
            count += 1
    return count


def calculate_provider_utilization(provider: 'Provider', assignments, target_date) -> float:
    """
    Calculate provider utilization percentage for a given date.
    
    Args:
        provider: Provider object with capacity configuration
        assignments: List of appointment assignments
        target_date: Date to calculate utilization for
    
    Returns:
        float: Utilization percentage (0-100)
    """
    total_assigned_minutes = 0
    max_available_minutes = provider.capacity.max_hours_per_day * 60

    for assignment in assignments:
        if (assignment.provider == provider and
                assignment.assigned_date == target_date):
            total_assigned_minutes += assignment.appointment_data.duration_min

    return (total_assigned_minutes / max_available_minutes * 100) if max_available_minutes > 0 else 0


def is_provider_overbooked(provider: 'Provider', assignments, target_date) -> bool:
    """
    Check if provider is overbooked for a given date.
    
    Args:
        provider: Provider object
        assignments: List of appointment assignments
        target_date: Date to check
    
    Returns:
        bool: True if provider is overbooked, False otherwise
    """
    utilization = calculate_provider_utilization(provider, assignments, target_date)
    return utilization > 100.0


@with_config()  # Uses default_program_type from scheduler.yml
def provider_capacity_management(constraint_factory: ConstraintFactory, core_config=None, service_config=None, combined_config=None, **kwargs) -> Constraint:
    """Manage provider capacity and workload limits."""
    # Handle Timefold validation calls where factory is None
    if constraint_factory is None:
        return constraint_factory.for_each(AppointmentAssignment).filter(lambda x: False).as_constraint("no_op_constraint")
        
    return (constraint_factory
            .for_each(AppointmentAssignment)
            .filter(lambda assignment: assignment.provider is not None and
                                       assignment.assigned_date is not None)
            .penalize(HardSoftScore.ONE_SOFT, lambda assignment: _calculate_capacity_penalty(assignment))
            .as_constraint("Provider capacity management"))


def provider_capacity_management_constraints(factory: ConstraintFactory):
    """Return all provider capacity management constraints."""
    # Handle Timefold validation calls where factory is None
    if factory is None:
        return []
        
    return [
        provider_capacity_management(factory),
    ]


def _calculate_capacity_penalty(assignment: AppointmentAssignment) -> int:
    """Calculate penalty based on provider capacity constraints."""
    if assignment.provider is None or assignment.assigned_date is None:
        return 0

    penalty = 0
    provider = assignment.provider
    assigned_date = assignment.assigned_date
    appointment_data = assignment.appointment_data

    # Check daily task count limits
    if _exceeds_daily_task_count(provider, assigned_date):
        penalty += 5

    # Check daily task points limits
    if _exceeds_daily_task_points(provider, assigned_date, appointment_data):
        penalty += 4

    # Check daily hours limits
    if _exceeds_daily_hours(provider, assigned_date, appointment_data):
        penalty += 6

    # Check weekly hours limits
    if _exceeds_weekly_hours(provider, assigned_date, appointment_data):
        penalty += 5

    # Check emergency capacity reserves
    if _exceeds_emergency_capacity(provider, assigned_date):
        penalty += 4

    # Check specialization capacity
    penalty += _calculate_specialization_penalty(provider, appointment_data)

    return penalty


def _exceeds_daily_task_count(provider: 'Provider', assigned_date: date) -> bool:
    """Check if provider exceeds daily task count limit."""
    current_task_count = provider.current_task_count
    return current_task_count >= provider.capacity.max_tasks_count_in_day


def _exceeds_daily_task_points(provider: 'Provider', assigned_date: date, appointment_data: 'AppointmentData') -> bool:
    """Check if provider exceeds daily task points limit."""
    appointment_task_points = appointment_data.task_points or 5
    current_task_points = provider.current_task_count * 5
    return current_task_points + appointment_task_points > provider.capacity.max_allocated_task_points_in_day


def _exceeds_daily_hours(provider: 'Provider', assigned_date: date, appointment_data: 'AppointmentData') -> bool:
    """Check if provider exceeds daily hours limit."""
    appointment_hours = appointment_data.duration_min / 60.0
    current_daily_hours = provider.current_task_count * 1.5
    return current_daily_hours + appointment_hours > provider.capacity.max_hours_per_day


def _exceeds_weekly_hours(provider: 'Provider', assigned_date: date, appointment_data: 'AppointmentData') -> bool:
    """Check if provider exceeds weekly hours limit."""
    appointment_hours = appointment_data.duration_min / 60.0
    current_weekly_hours = provider.current_task_count * 7.5

    # Get weekly hours limit from availability or use default
    weekly_hours_limit = 40  # Default 40 hours per week
    if provider.availability is not None:
        weekly_hours_limit = provider.availability.max_hours_per_week

    return current_weekly_hours + appointment_hours > weekly_hours_limit


def _exceeds_emergency_capacity(provider: 'Provider', assigned_date: date) -> bool:
    """Check if provider exceeds emergency capacity reserves."""
    if not provider.critical:
        return False

    current_utilization = provider.current_task_count / provider.capacity.max_tasks_count_in_day
    return current_utilization >= 0.9


def _calculate_specialization_penalty(provider: 'Provider', appointment_data: 'AppointmentData') -> int:
    """Calculate penalty for specialization mismatches."""
    penalty = 0

    # Check required skills
    required_skills = appointment_data.required_skills
    if required_skills is not None and len(required_skills) > 0:
        matching_skills = 0
        for skill in required_skills:
            if skill in provider.skills:
                matching_skills += 1

        skill_match_ratio = matching_skills / len(required_skills)
        if skill_match_ratio < 1.0:
            penalty += int((1.0 - skill_match_ratio) * 3)

    # Check required role
    required_role = appointment_data.required_role
    if required_role is not None and required_role != "" and provider.role != required_role:
        penalty += 3

    return penalty
