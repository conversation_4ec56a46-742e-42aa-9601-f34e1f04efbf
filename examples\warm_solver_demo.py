#!/usr/bin/env python3
"""
Warm Solver Demo

This script demonstrates the warm solver functionality by running multiple
optimization jobs and showing the performance benefits of state persistence.
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import time
from datetime import date, datetime, timedelta
from loguru import logger

from assign_appointments import AssignAppointment<PERSON>ob
from day_plan import Day<PERSON><PERSON>Job
from src.solver.warm_solver_manager import WarmSolverManager


def setup_logging():
    """Setup logging for the demo."""
    logger.remove()  # Remove default handler
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    logger.add(
        "logs/warm_solver_demo.log",
        rotation="1 day",
        retention="7 days",
        level="DEBUG"
    )


def run_cold_start_demo():
    """Run demo with cold start (no warm solver)."""
    logger.info("=== COLD START DEMO ===")
    
    # Create job without warm solver
    assign_job = AssignAppointmentJob(warm_solver=False)
    
    # Run multiple iterations
    times = []
    for i in range(3):
        logger.info(f"Iteration {i+1}/3")
        start_time = time.time()
        
        result = assign_job.run()
        
        end_time = time.time()
        execution_time = end_time - start_time
        times.append(execution_time)
        
        logger.info(f"Iteration {i+1} completed in {execution_time:.2f}s")
        logger.info(f"Assigned: {result['summary']['assigned_appointments']}/{result['summary']['total_appointments']}")
    
    avg_time = sum(times) / len(times)
    logger.info(f"Cold start average time: {avg_time:.2f}s")
    return avg_time


def run_warm_solver_demo():
    """Run demo with warm solver enabled."""
    logger.info("=== WARM SOLVER DEMO ===")
    
    # Create job with warm solver
    assign_job = AssignAppointmentJob(warm_solver=True)
    
    # Run multiple iterations
    times = []
    for i in range(3):
        logger.info(f"Iteration {i+1}/3")
        start_time = time.time()
        
        result = assign_job.run()
        
        end_time = time.time()
        execution_time = end_time - start_time
        times.append(execution_time)
        
        logger.info(f"Iteration {i+1} completed in {execution_time:.2f}s")
        logger.info(f"Assigned: {result['summary']['assigned_appointments']}/{result['summary']['total_appointments']}")
    
    avg_time = sum(times) / len(times)
    logger.info(f"Warm solver average time: {avg_time:.2f}s")
    return avg_time


def run_day_plan_demo():
    """Run day plan demo with warm solver."""
    logger.info("=== DAY PLAN WARM SOLVER DEMO ===")
    
    # Create day plan job with warm solver
    day_plan_job = DayPlanJob(warm_solver=True)
    
    # Run for today and tomorrow
    target_dates = [date.today(), date.today() + timedelta(days=1)]
    
    times = []
    for i, target_date in enumerate(target_dates):
        logger.info(f"Day plan {i+1}/2 for {target_date}")
        start_time = time.time()
        
        result = day_plan_job.run(target_date)
        
        end_time = time.time()
        execution_time = end_time - start_time
        times.append(execution_time)
        
        logger.info(f"Day plan {i+1} completed in {execution_time:.2f}s")
        logger.info(f"Assigned: {result.assigned_appointments}/{result.total_appointments}")
    
    avg_time = sum(times) / len(times)
    logger.info(f"Day plan warm solver average time: {avg_time:.2f}s")
    return avg_time


def show_cache_info():
    """Show information about cached solver states."""
    logger.info("=== CACHE INFORMATION ===")
    
    warm_solver_manager = WarmSolverManager()
    cache_info = warm_solver_manager.get_cache_info()
    
    logger.info(f"Cache directory: {cache_info['cache_directory']}")
    
    if cache_info['cached_jobs']:
        for job_type, info in cache_info['cached_jobs'].items():
            logger.info(f"Job type: {job_type}")
            logger.info(f"  Timestamp: {info.get('timestamp', 'N/A')}")
            logger.info(f"  File size: {info.get('file_size', 'N/A')} bytes")
            logger.info(f"  Age: {info.get('age_hours', 'N/A'):.1f} hours")
    else:
        logger.info("No cached states found")


def clear_cache():
    """Clear all cached solver states."""
    logger.info("=== CLEARING CACHE ===")
    
    warm_solver_manager = WarmSolverManager()
    warm_solver_manager.clear_cache()
    logger.info("Cache cleared")


def main():
    """Main demo function."""
    setup_logging()
    
    logger.info("Starting Warm Solver Demo")
    logger.info("=" * 50)
    
    try:
        # Clear any existing cache
        clear_cache()
        
        # Run cold start demo
        cold_avg = run_cold_start_demo()
        
        logger.info("=" * 50)
        
        # Run warm solver demo
        warm_avg = run_warm_solver_demo()
        
        logger.info("=" * 50)
        
        # Show cache information
        show_cache_info()
        
        logger.info("=" * 50)
        
        # Run day plan demo
        day_plan_avg = run_day_plan_demo()
        
        logger.info("=" * 50)
        
        # Show performance comparison
        logger.info("=== PERFORMANCE COMPARISON ===")
        logger.info(f"Cold start average: {cold_avg:.2f}s")
        logger.info(f"Warm solver average: {warm_avg:.2f}s")
        
        if cold_avg > 0:
            improvement = ((cold_avg - warm_avg) / cold_avg) * 100
            logger.info(f"Warm solver improvement: {improvement:.1f}%")
        
        logger.info(f"Day plan warm solver average: {day_plan_avg:.2f}s")
        
        # Show final cache info
        logger.info("=" * 50)
        show_cache_info()
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        raise


if __name__ == "__main__":
    main() 