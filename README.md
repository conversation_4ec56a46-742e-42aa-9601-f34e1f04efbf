# CareAXL Scheduling Engine

A sophisticated healthcare appointment scheduling system powered by Timefold constraint optimization, featuring a **two-stage optimization architecture** for realistic healthcare scheduling workflows.

## 🏗️ Two-Stage Optimization Architecture

The system uses a two-stage approach to manage complexity and optimize different aspects separately:

### Stage 1: Assignment Solver (Strategic Planning)
- **Purpose**: Assigns **provider** and **date** to appointment requests
- **Schedule**: Runs nightly at 2:00 AM
- **Output**: Appointments with provider + date assigned, but no specific time
- **Use Case**: Strategic planning for the upcoming week (configurable rolling window)

### Stage 2: Day Plan Solver (Operational Planning)
- **Purpose**: Assigns **time slots** and optimizes **visit orders** for daily schedules
- **Schedule**: Runs daily at 6:00 AM
- **Input**: Appointments already assigned to that day with providers
- **Output**: Complete daily schedule with optimized routes and visit sequences
- **Use Case**: Daily operational planning with route optimization

## 🎯 Key Features

- **AI-Powered Optimization**: Uses Timefold constraint solver for complex scheduling problems
- **Two-Stage Design**: Separates strategic from tactical decisions
- **Healthcare-Specific Constraints**: Built for real healthcare workflows
- **Configuration-Driven**: Flexible constraint system with feature toggles
- **Warm Solver Support**: Performance optimization through state persistence
- **Hybrid Integration**: REST API + Event-driven architecture
- **Multi-Service Support**: Skilled nursing, behavioral care, hospital-at-home, PCS

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd caxl-scheduling-engine

# Install the package in development mode
pip install -e .

# Or install with development dependencies
pip install -e ".[dev]"

# Or install with API dependencies
pip install -e ".[api]"
```

### Running the Scheduler

#### Command Line Interface

```bash
# Run assignment job once (Stage 1)
python scheduler.py --mode once --job assign

# Run day planning job once (Stage 2)
python scheduler.py --mode once --job dayplan --date 2024-07-01

# Run with warm solver enabled
python scheduler.py --mode once --job assign --warm-solver

# Run as daemon (scheduled execution)
python scheduler.py --mode daemon

# Run with custom config folder
python scheduler.py --mode once --job assign --config-folder /path/to/config
```

#### Programmatic Usage

```python
from scheduler import AppointmentScheduler

# Create scheduler instance
scheduler = AppointmentScheduler()

# Run assignment optimization
result = scheduler.run_assign_appointments()

# Run day planning optimization
day_result = scheduler.run_day_plan(target_date)
```

## 📋 Configuration

### Global Configuration (`config/scheduler.yml`)

```yaml
# System-wide settings
rolling_window_days: 7
batch_size: 100
max_solving_time_seconds: 300
log_level: INFO

# Feature toggles
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true

# Scheduling times
assign_appointment_time: "02:00"
day_plan_time: "06:00"
```



## 🏥 Program Types

The system supports multiple healthcare program types:

- **Skilled Nursing**: Home health nursing services
- **Behavioral Care**: Mental health and behavioral therapy
- **Hospital at Home**: Acute care in home setting
- **Personal Care Services (PCS)**: Daily living assistance

## 🔧 Constraints

### AssignAppointment Job Constraints

- **Hard Constraints**:
    - Required provider skills
    - Geographic service area
    - Timed visit date requirements
    - Provider capacity limits

- **Soft Constraints**:
    - Workload balancing
    - Geographic clustering
    - Patient preferences
    - Continuity of care

### DayPlan Job Constraints

- **Hard Constraints**:
    - All appointments must have time assigned
    - No double booking of providers

- **Soft Constraints**:
    - Preferred working hours
    - Workload balance across time slots

## 📊 Output Examples

### AssignAppointment Job Output

```
=== Assignment Results ===
Batch ID: assign_20240115_020000
Total Appointments: 25
Assigned: 23
Unassigned: 2
Processing Time: 45.23s

=== Assignment Details ===
✅ John Smith
   Service: skilled_nursing (FLEXIBLE)
   Provider: Nurse Johnson on 2024-01-16
   ✅ Satisfied: provider_assigned, date_assigned, required_skills, service_area

❌ Mary Wilson
   Service: physical_therapy (TIMED)
   Provider: Unassigned
   ❌ Violated: no_provider_assigned, no_date_assigned
```

### DayPlan Job Output

```
=== Day Plan Results ===
Date: 2024-01-15
Batch ID: dayplan_20240115_060000
Total Appointments: 8
Time Assigned: 7
No Time Assigned: 1
Processing Time: 12.45s

=== Time Assignment Details ===
✅ Patient John Smith -> Provider Nurse Johnson -> Time 09:00-10:00
✅ Patient Mary Wilson -> Provider Dr. Brown -> Time 14:00-15:00
❌ Patient Bob Davis -> Provider Nurse Smith -> Time None
```

## 🗂️ Project Structure

```
caxl-scheduling-engine/
├── src/
│   ├── api/                    # REST API and service integration
│   ├── constraints/           # Constraint system
│   ├── data/                  # Data management
│   ├── events/                # Event system
│   ├── model/                 # Domain models
│   ├── services/              # Business logic
│   ├── solver/                # Optimization engine
│   ├── main.py                # Main entry point
│   └── __init__.py
├── config/                    # Configuration files
│   ├── scheduler.yml          # Core configuration
│   ├── skilled_nursing.yml    # Service-specific configs
│   ├── behavioral_care.yml
│   ├── hospital_at_home.yml
│   └── pcs.yml
├── docs/                      # Documentation
│   ├── CAREAXL_SCHEDULING_ENGINE_REFERENCE.md
│   ├── Scheduling Engine (Assignment and Day Plan).xlsx
│   └── __init__.py
├── examples/                  # Example scripts
│   ├── end_to_end_example.py
│   ├── warm_solver_demo.py
│   ├── hybrid_architecture_demo.py
│   ├── rabbitmq_integration_demo.py
│   ├── file_event_logging_demo.py
│   └── simple_hybrid_demo.py
├── tests/                     # Test suite
├── logs/                      # Log files
├── assign_appointments.py     # Stage 1 job
├── day_plan.py               # Stage 2 job
├── scheduler.py              # Main scheduler
├── pyproject.toml            # Project configuration
├── Dockerfile                # Production container
├── Dockerfile.dev            # Development container
├── docker-compose.yml        # Docker compose
├── docker-compose.api.yml    # API service compose
├── __init__.py
└── README.md
```

## 🔄 Workflow

1. **Nightly (2:00 AM)**: AssignAppointment job runs
    - Processes new appointment requests
    - Assigns date and provider based on constraints
    - Creates `ScheduledAppointment` records

2. **Daily (6:00 AM)**: DayPlan job runs
    - Loads appointments scheduled for that day
    - Assigns specific time slots
    - Creates final schedule

3. **Real-time**: System can handle urgent changes
    - Emergency appointments can be added
    - Cancellations can be processed
    - Schedule adjustments can be made

## 🛠️ Customization

### Adding New Program Types

1. Create configuration file: `config/new_program.yml`
2. Add program type to domain models
3. Update constraints if needed

### Modifying Constraints

- Edit `constraints.py` for AssignAppointment constraints
- Edit `constraints.py` for DayPlan constraints
- Update configuration files for weights and parameters



## 📈 Monitoring and Logging

- **Log Files**: All jobs log to `logs/` directory
- **Metrics**: Processing time, success rates, constraint violations
- **Alerts**: Failed jobs, high violation rates, capacity issues

 