#!/usr/bin/env python3
"""
DayPlan Job

This job runs daily to assign specific time slots to appointments that have already been
assigned a date and provider by the AssignAppointment job.

Schedule: Runs daily in the morning
Purpose: Time slot assignment for the day's appointments
Features: Includes route optimization for visit order and travel time minimization
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), 'src')))
import time as time_module
from datetime import datetime, date, time
from typing import List, Optional

from loguru import logger
from timefold.solver import SolverFactory
from timefold.solver.config import SolverConfig, ScoreDirectorFactoryConfig, TerminationConfig, Duration

from src.constraints.day_constraints import define_day_constraints
from src.model.domain import (
    Provider, BatchAssignmentResult, AssignmentResult
)
from src.model.planning_models import (
    DaySchedule, TimeSlotAssignment, ScheduledAppointment
)
from src.constraints.config_registry import ConfigRegistry
from src.data.data_loader import create_demo_scheduled_appointments
from src.solver.warm_solver_manager import WarmSolverManager


class DayPlanJob:
    """Job for assigning time slots to daily appointments with route optimization."""

    def __init__(self, config_folder: str = "config", daemon_mode: bool = False, warm_solver: bool = False):
        # Load configurations using the new registry
        ConfigRegistry.load_configurations(config_folder)
        self.scheduler_config = ConfigRegistry.get_core_config('scheduler')
        self.daemon_mode = daemon_mode
        
        # Initialize warm solver support
        warm_solver_config = ConfigRegistry.get_warm_solver_config()
        self.warm_solver_enabled = warm_solver and warm_solver_config.get('enabled', False)
        self.warm_solver_manager = WarmSolverManager(
            warm_solver_config.get('cache_directory', 'cache/warm_solver')
        ) if self.warm_solver_enabled else None
        
        logger.info(f"DayPlan warm solver enabled: {self.warm_solver_enabled}")
        
        # Configuration is now handled automatically by decorators

    def run(self, target_date: Optional[date] = None, batch_id: Optional[str] = None) -> BatchAssignmentResult:
        """
        Run the day planning job for a specific date.
        
        Args:
            target_date: Date to plan for (defaults to today)
            batch_id: Optional batch identifier for tracking
            
        Returns:
            BatchAssignmentResult with time assignment outcomes
        """
        start_time = time_module.time()
        target_date = target_date or date.today()
        batch_id = batch_id or f"dayplan_{target_date.strftime('%Y%m%d')}_{datetime.now().strftime('%H%M%S')}"

        log_file_path = os.path.join('logs', f'day_plan_{datetime.now().strftime("%Y-%m-%d")}.log')
        logger.info("DAY PLAN JOB STARTED")
        logger.info(f"Target Date: {target_date}")
        logger.info(f"Batch ID: {batch_id}")
        logger.info(f"Log file: {log_file_path}")

        try:
            # Load scheduled appointments for the target date
            logger.info("=== STAGE 1: Loading Scheduled Appointments ===")
            scheduled_appointments = self._load_scheduled_appointments(target_date)
            logger.info(f"Loaded {len(scheduled_appointments)} scheduled appointments for {target_date}")

            logger.info("=== STAGE 2: Loading Available Time Slots ===")
            time_slots = self._load_available_time_slots(target_date)
            logger.info(f"Loaded {len(time_slots)} time slots for {target_date}")

            if not scheduled_appointments:
                logger.warning("No appointments scheduled for this date")
                processing_time = time_module.time() - start_time
                logger.info(f"Processing Time: {processing_time:.2f}s")
                logger.info("DAY PLAN JOB COMPLETED")
                print(f"No appointments scheduled for {target_date}. Processing Time: {processing_time:.2f}s")
                print(f"Log file: {log_file_path}")
                return self._create_empty_result(batch_id, start_time)

            # Create time slot assignments
            logger.info("=== STAGE 3: Creating Time Slot Assignments ===")
            time_assignments = self._create_time_assignments(scheduled_appointments)
            logger.info(f"Created {len(time_assignments)} time slot assignments")

            # Create the day scheduling problem
            day_schedule = DaySchedule(
                id=batch_id,
                date=target_date,
                time_slots=time_slots,
                scheduled_appointments=scheduled_appointments,
                time_assignments=time_assignments
            )

            # Solve the time assignment problem
            logger.info("=== STAGE 4: Solving Time Assignment Problem ===")
            best_solution = self._solve_time_assignment_problem(day_schedule)

            # Process results
            logger.info("=== STAGE 5: Processing Results ===")
            results = self._process_time_assignment_results(best_solution)

            # Display detailed visit order
            self._display_visit_order(best_solution)

            # Calculate statistics
            assigned_count = len([r for r in results if r.time_slot_id])
            unassigned_count = len(results) - assigned_count
            average_score = sum(r.score for r in results) / len(results) if results else 0.0

            processing_time = time_module.time() - start_time

            logger.info(
                f"Time assignment completed: {assigned_count}/{len(results)} assigned in {processing_time:.2f}s")
            logger.info(f"Average Score: {average_score:.2f}")
            logger.info("DAY PLAN JOB COMPLETED")
            print(
                f"Day plan completed for {target_date}: {assigned_count}/{len(results)} assigned. Processing Time: {processing_time:.2f}s")
            print(f"Log file: {log_file_path}")

            result = BatchAssignmentResult(
                batch_id=batch_id,
                total_appointments=len(results),
                assigned_appointments=assigned_count,
                unassigned_appointments=unassigned_count,
                average_score=average_score,
                processing_time_seconds=processing_time,
                results=results
            )

            self._handle_completion(result)
            return result

        except Exception as e:
            logger.error(f"Error in DayPlan job: {e}", exc_info=True)
            print(f"Error in DayPlan job: {e}")
            print(f"Log file: {log_file_path}")
            raise

    def _setup_route_optimization(self, time_assignments: List[TimeSlotAssignment]):
        """Set up route optimization by configuring the global assignments variable."""
        # Route optimization constraints are temporarily disabled due to Timefold API bug
        logger.info(f"Route optimization disabled - {len(time_assignments)} assignments available")

    def _load_scheduled_appointments(self, target_date: date) -> List['ScheduledAppointment']:
        """Load appointments already scheduled for the target date."""
        # Load data using data loader
        return create_demo_scheduled_appointments(target_date)

    def _load_available_time_slots(self, target_date: date) -> List[time]:
        """Load available time slots for the target date."""
        # Create 30-minute slots from 8 AM to 6 PM
        time_slots = []
        for hour in range(8, 18):
            for minute in [0, 30]:
                time_slots.append(time(hour, minute))
        return time_slots

    def _create_time_assignments(self, scheduled_appointments: List[ScheduledAppointment]) -> List[TimeSlotAssignment]:
        """Create time slot assignments for scheduled appointments."""
        assignments = []
        for appointment in scheduled_appointments:
            assignment = TimeSlotAssignment(
                id=f"time_assignment_{appointment.id}",
                scheduled_appointment=appointment,
                time_slot=None  # Will be assigned by solver
            )
            assignments.append(assignment)
        return assignments

    def _solve_time_assignment_problem(self, day_schedule: DaySchedule) -> DaySchedule:
        """Solve the time slot assignment problem using Timefold."""
        logger.info(f"Solving time assignment problem with {len(day_schedule.time_assignments)} appointments")
        logger.info("Route optimization constraints enabled")

        # Handle warm solver mode
        if self.warm_solver_enabled:
            return self._solve_with_warm_solver(day_schedule)
        else:
            return self._solve_with_cold_start(day_schedule)
    
    def _solve_with_warm_solver(self, day_schedule: DaySchedule) -> DaySchedule:
        """Solve using warm solver with state persistence."""
        logger.info("Using warm solver mode for day planning")
        
        # Check if warm solver manager is available
        if not self.warm_solver_manager:
            logger.warning("Warm solver manager not available, falling back to cold start")
            return self._solve_with_cold_start(day_schedule)
        
        # Try to load cached state
        cached_state = self.warm_solver_manager.load_solver_state('dayplan')
        
        if cached_state:
            logger.info("Found cached day plan state, attempting incremental update")
            try:
                # Merge new appointments with cached solution
                merged_schedule = self._merge_with_cached_state(day_schedule, cached_state)
                
                # Create solver and solve
                solver_config = self._create_solver_config()
                solver_factory = SolverFactory.create(solver_config)
                solver = solver_factory.build_solver()
                solution = solver.solve(merged_schedule)
                
                # Save updated state
                metadata = {
                    'appointment_count': len(day_schedule.time_assignments),
                    'date': day_schedule.date.isoformat(),
                    'time_slot_count': len(day_schedule.time_slots)
                }
                self.warm_solver_manager.save_solver_state('dayplan', solver, solution, metadata)
                
                logger.info("Warm solver completed successfully")
                return solution
                
            except Exception as e:
                logger.warning(f"Warm solver failed, falling back to cold start: {e}")
                # Fall back to cold start
                pass
        
        # Cold start (no cache or cache failed)
        logger.info("Performing cold start optimization")
        solution = self._solve_with_cold_start(day_schedule)
        
        # Save state for next run
        if self.warm_solver_manager:
            metadata = {
                'appointment_count': len(day_schedule.time_assignments),
                'date': day_schedule.date.isoformat(),
                'time_slot_count': len(day_schedule.time_slots)
            }
            self.warm_solver_manager.save_solver_state('dayplan', None, solution, metadata)
        
        return solution
    
    def _solve_with_cold_start(self, day_schedule: DaySchedule) -> DaySchedule:
        """Solve using cold start (no cached state)."""
        logger.info("Performing cold start optimization")
        
        # Create solver configuration
        solver_config = self._create_solver_config()
        logger.info(f"Created solver config with {min(self.scheduler_config.get('max_solving_time_seconds', 60) if self.scheduler_config else 60, 60)}s timeout")

        # Create solver
        logger.info("Building solver...")
        solver_factory = SolverFactory.create(solver_config)
        solver = solver_factory.build_solver()
        logger.info("Solver built successfully")

        # Solve the problem
        logger.info("Starting solver...")
        start_time = time_module.time()
        try:
            best_solution = solver.solve(day_schedule)
            solving_time = time_module.time() - start_time
            logger.info(f"Solving completed in {solving_time:.2f} seconds")
            if best_solution.score:
                logger.info(f"Solution score: {best_solution.score}")
            return best_solution
        except Exception as e:
            solving_time = time_module.time() - start_time
            logger.error(f"Solver failed after {solving_time:.2f} seconds: {e}")
            raise
    
    def _create_solver_config(self) -> SolverConfig:
        """Create solver configuration."""
        return SolverConfig(
            solution_class=DaySchedule,
            entity_class_list=[TimeSlotAssignment],
            score_director_factory_config=ScoreDirectorFactoryConfig(
                constraint_provider_function=define_day_constraints
            ),
            termination_config=TerminationConfig(
                spent_limit=Duration(seconds=min(self.scheduler_config.get('max_solving_time_seconds', 60) if self.scheduler_config else 60, 60))
                # Shorter timeout for daily planning
            )
        )
    
    def _merge_with_cached_state(self, new_schedule: DaySchedule, cached_state: dict) -> DaySchedule:
        """Merge new schedule with cached state for incremental updates."""
        cached_solution = cached_state.get('solution')
        
        if not cached_solution:
            logger.warning("No cached solution found, using new schedule as-is")
            return new_schedule
        
        # For now, return the new schedule as-is
        # In a full implementation, this would merge new appointments with existing time assignments
        logger.info("Merging new appointments with cached day plan state")
        
        # TODO: Implement proper merging logic
        # This would involve:
        # 1. Identifying new appointments not in cached solution
        # 2. Preserving existing time assignments for unchanged appointments
        # 3. Creating new time assignment entities for new appointments
        # 4. Updating time slot availability based on cached assignments
        
        return new_schedule

    def _process_time_assignment_results(self, solution: DaySchedule) -> List[AssignmentResult]:
        """Process the solution and create assignment results."""
        # Load the same demo data that was used to create scheduled appointments
        from src.data.data_loader import create_demo_data
        demo_data = create_demo_data()
        consumers = demo_data["consumers"]
        consumer_lookup = {str(consumer.id): consumer.name for consumer in consumers}

        results = []

        for assignment in solution.time_assignments:
            consumer_id = str(assignment.scheduled_appointment.appointment_data.consumer_id)
            patient_name = consumer_lookup.get(consumer_id, f"Unknown (ID: {consumer_id})")

            result = AssignmentResult(
                appointment_id=assignment.id,
                patient_id=patient_name,  # Use patient name instead of ID
                provider_id=str(assignment.scheduled_appointment.provider.id),
                time_slot_id=assignment.time_slot.strftime("%H:%M") if assignment.time_slot else "",
                score=0.0,  # Would be calculated based on constraints
                constraints_satisfied=self._get_satisfied_constraints(assignment, solution),
                constraints_violated=self._get_violated_constraints(assignment, solution)
            )
            results.append(result)

        return results

    def _get_satisfied_constraints(self, assignment: TimeSlotAssignment, solution: DaySchedule) -> List[str]:
        """Get list of satisfied constraints for this time assignment."""
        satisfied = []

        if assignment.time_slot is not None:
            satisfied.append("time_assigned")

            # Check if time is within provider's preferred hours
            if self._is_within_preferred_hours(assignment.scheduled_appointment.provider, assignment.time_slot):
                satisfied.append("preferred_hours")

            # Check if no double booking
            if not self._is_double_booked(assignment, solution):
                satisfied.append("no_double_booking")

        return satisfied

    def _get_violated_constraints(self, assignment: TimeSlotAssignment, solution: DaySchedule) -> List[str]:
        """Get list of violated constraints for this time assignment."""
        violated = []

        if assignment.time_slot is None:
            violated.append("no_time_assigned")
        else:
            # Check if time is within provider's preferred hours
            if not self._is_within_preferred_hours(assignment.scheduled_appointment.provider, assignment.time_slot):
                violated.append("outside_preferred_hours")

            # Check if double booked
            if self._is_double_booked(assignment, solution):
                violated.append("double_booking")

        return violated

    def _is_within_preferred_hours(self, provider: Provider, time_slot: time) -> bool:
        """Check if time slot is within provider's preferred working hours."""
        if provider.availability is None:
            # Default to standard healthcare hours if no availability configured
            return 8 <= time_slot.hour < 18

        # Check if provider has working hours configured
        if provider.availability.working_hours:
            start_time, end_time = provider.availability.working_hours
            return start_time <= time_slot < end_time

        # Check against shift patterns
        if provider.availability.primary_shift:
            shift_start = provider.availability.primary_shift.shift_start
            shift_end = provider.availability.primary_shift.shift_end

            if provider.availability.primary_shift.crosses_midnight:
                # Handle overnight shifts
                return time_slot >= shift_start or time_slot < shift_end
            else:
                return shift_start <= time_slot < shift_end

        # Default to standard hours if no specific configuration
        return 8 <= time_slot.hour < 18

    def _is_double_booked(self, assignment: TimeSlotAssignment, solution: DaySchedule) -> bool:
        """Check if provider is double booked at this time."""
        if assignment.time_slot is None:
            return False

        provider = assignment.scheduled_appointment.provider
        target_time = assignment.time_slot
        target_date = assignment.scheduled_appointment.assigned_date

        # Check against other assignments for the same provider and time
        for other_assignment in solution.time_assignments:
            if other_assignment == assignment:
                continue

            if (other_assignment.scheduled_appointment.provider == provider and
                    other_assignment.time_slot == target_time and
                    other_assignment.scheduled_appointment.assigned_date == target_date):
                return True

        return False

    def _create_empty_result(self, batch_id: str, start_time: float) -> BatchAssignmentResult:
        """Create an empty result when no appointments are available."""
        return BatchAssignmentResult(
            batch_id=batch_id,
            total_appointments=0,
            assigned_appointments=0,
            unassigned_appointments=0,
            average_score=0.0,
            processing_time_seconds=time_module.time() - start_time,
            results=[]
        )

    def _display_visit_order(self, solution: DaySchedule):
        """Display detailed visit order information after the day plan is complete."""
        logger.info("\n=== Detailed Visit Order ===")

        # Load the same demo data that was used to create scheduled appointments
        from src.data.data_loader import create_demo_data
        demo_data = create_demo_data()
        consumers = demo_data["consumers"]
        consumer_lookup = {str(consumer.id): consumer.name for consumer in consumers}

        # Group assignments by provider
        provider_assignments = {}
        for assignment in solution.time_assignments:
            if assignment.time_slot is None:
                continue

            provider = assignment.scheduled_appointment.provider
            provider_id = str(getattr(provider, 'id', 'Unknown'))
            provider_name = getattr(provider, 'name', 'Unknown Provider')

            if provider_id not in provider_assignments:
                provider_assignments[provider_id] = {
                    'name': provider_name,
                    'assignments': []
                }

            # Get appointment details
            appointment_data = assignment.scheduled_appointment.appointment_data
            consumer_id = str(getattr(appointment_data, 'consumer_id', 'Unknown'))
            patient_name = consumer_lookup.get(consumer_id, f"Unknown (ID: {consumer_id})")

            # Get appointment date
            appointment_date = getattr(assignment.scheduled_appointment, 'assigned_date', None)
            date_str = appointment_date.strftime('%Y-%m-%d') if appointment_date else 'Unknown Date'

            # Get location details
            location = getattr(appointment_data, 'location', None)
            if location:
                location_name = getattr(location, 'city', 'Unknown City')
                location_address = getattr(location, 'address', 'No Address')
            else:
                location_name = 'Unknown Location'
                location_address = 'No Address'

            # Get time slot details - time_slot is a time object, not a slot object
            time_slot = assignment.time_slot
            if time_slot:
                # Calculate end time based on appointment duration
                duration_min = getattr(appointment_data, 'duration_min', 30)
                start_minutes = time_slot.hour * 60 + time_slot.minute
                end_minutes = start_minutes + duration_min
                end_hour = end_minutes // 60
                end_minute = end_minutes % 60
                end_time = time(end_hour, end_minute)
                time_str = f"{time_slot.strftime('%H:%M')}-{end_time.strftime('%H:%M')}"
            else:
                time_str = 'No Time Assigned'

            provider_assignments[provider_id]['assignments'].append({
                'patient_name': patient_name,
                'patient_id': consumer_id,
                'location_name': location_name,
                'location_address': location_address,
                'time': time_str,
                'time_slot': time_slot,
                'date': date_str
            })

        # Sort assignments by time slot for each provider
        for provider_id in provider_assignments:
            provider_assignments[provider_id]['assignments'].sort(
                key=lambda x: x['time_slot'] if x['time_slot'] else time(23, 59)
            )

        # Display assignments
        for provider_id, provider_info in provider_assignments.items():
            logger.info(f"\nProvider: {provider_info['name']} (ID: {provider_id})")
            logger.info("=" * 60)

            if not provider_info['assignments']:
                logger.info("   No appointments assigned")
                continue

            for i, assignment in enumerate(provider_info['assignments'], 1):
                logger.info(f"   {i:2d}. {assignment['time']} | Patient: {assignment['patient_name']} | Date: {assignment['date']}")
                logger.info(f"       Location: {assignment['location_name']}")
                logger.info(f"       Address: {assignment['location_address']}")
                logger.info("")

        # Summary statistics
        total_visits = sum(len(info['assignments']) for info in provider_assignments.values())
        logger.info(f"\nVisit Order Summary:")
        logger.info(f"   Total Providers: {len(provider_assignments)}")
        logger.info(f"   Total Visits: {total_visits}")
        logger.info(
            f"   Average Visits per Provider: {total_visits / len(provider_assignments):.1f}" if provider_assignments else "0")
        logger.info("=" * 60)

    def _handle_completion(self, result: BatchAssignmentResult):
        """Handle job completion based on daemon mode."""
        if not self.daemon_mode:
            logger.info("Job completed.")
            # Don't exit when called programmatically (e.g., from API)
        else:
            logger.info("Job completed. Continuing in daemon mode...")


def main(daemon_mode: bool = False):
    """
    Main entry point for the DayPlan job.
    
    Args:
        daemon_mode: If True, the job will not exit after completion (for continuous operation)
    """
    import argparse
    from datetime import date
    import sys

    parser = argparse.ArgumentParser(description='Assign time slots to daily appointments')
    parser.add_argument('--config-folder', default='config', help='Configuration folder path')
    parser.add_argument('--date', help='Target date (YYYY-MM-DD format, defaults to today)')
    parser.add_argument('--batch-id', help='Optional batch identifier')
    parser.add_argument('--daemon', '-d', action='store_true', help='Run in daemon mode (do not exit after completion)')

    args = parser.parse_args()

    # Override daemon mode if specified in arguments
    if args.daemon:
        daemon_mode = True

    try:
        target_date = None
        if args.date:
            target_date = datetime.strptime(args.date, '%Y-%m-%d').date()

        job = DayPlanJob(args.config_folder, daemon_mode)
        result = job.run(target_date, args.batch_id)

        print(f"\n=== Day Plan Results ===")
        print(f"Date: {target_date or date.today()}")
        print(f"Batch ID: {result.batch_id}")
        print(f"Total Appointments: {result.total_appointments}")
        print(f"Time Assigned: {result.assigned_appointments}")
        print(f"No Time Assigned: {result.unassigned_appointments}")
        print(f"Average Score: {result.average_score:.2f}")
        print(f"Processing Time: {result.processing_time_seconds:.2f}s")

        if result.results:
            print(f"\n=== Time Assignment Details ===")
            for assignment_result in result.results:
                status = "[OK]" if assignment_result.time_slot_id else "[NO TIME]"
                print(
                    f"{status} Patient {assignment_result.patient_id} -> Provider {assignment_result.provider_id} -> Time {assignment_result.time_slot_id or 'None'}")

        # Exit if not in daemon mode and running as main script
        if not daemon_mode and __name__ == "__main__":
            logger.info("Day plan job completed. Exiting...")
            sys.exit(0)
        else:
            logger.info("Day plan job completed. Continuing in daemon mode...")

    except Exception as e:
        logger.error(f"Day plan job failed: {e}")
        if not daemon_mode and __name__ == "__main__":
            sys.exit(1)
        raise


if __name__ == "__main__":
    main()
