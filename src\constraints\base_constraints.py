"""
Base constraint functions for healthcare scheduling optimization.

This module provides common decorators used across different constraint modules.
Only decorators that are used across all constraints should be here.
"""

import functools
from .config_registry import ConfigRegistry
from .service_context import get_current_service_type

def with_config(service_name=None, return_type='constraint'):
    """
    Decorator for constraints that need configuration.
    Automatically provides core_config, service_config, and combined_config.
    
    Args:
        service_name: Program type name (e.g., 'behavioral_care', 'hospital_at_home').
                     If None, uses default_program_type from scheduler.yml
        return_type: Type of return value ('constraint' or 'list')
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Handle Timefold validation calls where factory is None
            if args and args[0] is None:
                if return_type == 'list':
                    return []
                from model.planning_models import AppointmentAssignment
                return type('NoOpConstraint', (), {'as_constraint': staticmethod(lambda name: None), 'filter': staticmethod(lambda f: None)})()
            
            # Get core config first to access default program type
            core_config = ConfigRegistry.get_core_config('scheduler')
            
            # Determine program type: runtime override > decorator param > config default
            runtime_service = get_current_service_type()
            if runtime_service:
                actual_service = runtime_service
            elif service_name:
                actual_service = service_name
            else:
                # Use default from scheduler.yml
                actual_service = core_config.get('default_program_type', 'skilled_nursing') if core_config else 'skilled_nursing'
            
            # Set service context and get configs
            ConfigRegistry.set_current_service_context(actual_service)
            service_config = ConfigRegistry.get_service_config(actual_service)
            
            # Create combined config (service overrides core)
            combined_config = {**(core_config or {}), **(service_config or {})}
            
            # Call function with all configs
            try:
                return func(*args, core_config=core_config, service_config=service_config, combined_config=combined_config, **kwargs)
            finally:
                ConfigRegistry.clear_current_service_context()
        return wrapper
    return decorator
